#!/usr/bin/env python3
"""
Test script to reproduce the exact duplicate issue in nested archive save-back
This simulates the PropagateChangesUpward scenario with multiple nested levels
"""

import os
import zipfile
import tempfile
import shutil
from pathlib import Path

def create_nested_archive_structure():
    """Create a realistic nested archive structure that might trigger duplicates"""
    print("🔧 Creating nested archive structure...")
    
    # Level 3: Innermost archive
    with zipfile.ZipFile('level3.zip', 'w') as zf:
        zf.writestr('deepfile.txt', 'Content at level 3')
        zf.writestr('config.json', '{"level": 3, "name": "deep"}')
    
    # Level 2: Middle archive containing level3
    with zipfile.ZipFile('level2.zip', 'w') as zf:
        zf.writestr('middlefile.txt', 'Content at level 2')
        with open('level3.zip', 'rb') as f:
            zf.writestr('nested/level3.zip', f.read())
    
    # Level 1: Root archive containing level2
    with zipfile.ZipFile('level1.zip', 'w') as zf:
        zf.writestr('rootfile.txt', 'Content at level 1')
        with open('level2.zip', 'rb') as f:
            zf.writestr('level2.zip', f.read())
        zf.writestr('readme.md', '# Nested Archive Test\nThis tests deep nesting')
    
    # Clean up intermediate files
    os.remove('level3.zip')
    os.remove('level2.zip')
    
    print("✅ Nested archive structure created: level1.zip")
    return 'level1.zip'

def detailed_analysis_with_content(archive_path, name="Archive"):
    """Enhanced analysis that checks content as well as names"""
    print(f"\n🔍 ENHANCED ANALYSIS: {name}")
    print("-" * 60)
    
    try:
        with zipfile.ZipFile(archive_path, 'r') as zf:
            entries = zf.namelist()
            print(f"📊 Total entries: {len(entries)}")
            
            # Check for exact duplicates
            entry_counts = {}
            content_map = {}
            
            for entry in entries:
                # Count occurrences
                if entry in entry_counts:
                    entry_counts[entry] += 1
                else:
                    entry_counts[entry] = 1
                
                # Check content for duplicate detection
                try:
                    content = zf.read(entry)
                    content_hash = hash(content)
                    
                    if content_hash in content_map:
                        content_map[content_hash].append(entry)
                    else:
                        content_map[content_hash] = [entry]
                except:
                    # Skip directories or unreadable entries
                    pass
            
            # Report exact name duplicates
            exact_duplicates = [entry for entry, count in entry_counts.items() if count > 1]
            if exact_duplicates:
                print(f"❌ EXACT NAME DUPLICATES: {exact_duplicates}")
                for dup in exact_duplicates:
                    print(f"   '{dup}' appears {entry_counts[dup]} times")
            
            # Report content duplicates (different names, same content)
            content_duplicates = [entries for entries in content_map.values() if len(entries) > 1]
            if content_duplicates:
                print(f"⚠️  CONTENT DUPLICATES: {content_duplicates}")
            
            if not exact_duplicates and not content_duplicates:
                print("✅ No duplicates detected")
            
            # List all entries
            print(f"\n📋 All entries:")
            for i, entry in enumerate(entries):
                info = zf.getinfo(entry)
                print(f"   {i+1:2d}. 📄 '{entry}' ({info.file_size} bytes)")
            
            return exact_duplicates, content_duplicates
            
    except Exception as ex:
        print(f"❌ Error analyzing {name}: {ex}")
        return [], []

def simulate_nested_save_back():
    """Simulate the exact nested save-back process that causes duplicates"""
    print("\n🔄 Simulating nested save-back with PropagateChangesUpward...")
    
    archive_path = create_nested_archive_structure()
    
    # Analyze BEFORE
    before_exact, before_content = detailed_analysis_with_content(archive_path, "BEFORE Nested Save-Back")
    
    # Simulate navigation to level 3 and modification
    print("\n🔧 Simulating navigation to level 3 and file modification...")
    
    # Step 1: Extract level2.zip from level1.zip (like NavigateInto would do)
    temp_dir = tempfile.mkdtemp(prefix='NestedSaveBack_')
    level2_path = os.path.join(temp_dir, 'level2.zip')
    
    with zipfile.ZipFile(archive_path, 'r') as zf:
        with zf.open('level2.zip') as source:
            with open(level2_path, 'wb') as target:
                target.write(source.read())
    
    # Step 2: Extract level3.zip from level2.zip
    level3_path = os.path.join(temp_dir, 'level3.zip')
    
    with zipfile.ZipFile(level2_path, 'r') as zf:
        with zf.open('nested/level3.zip') as source:
            with open(level3_path, 'wb') as target:
                target.write(source.read())
    
    # Step 3: Modify a file in level3 (simulate user edit)
    temp_extract_level3 = os.path.join(temp_dir, 'level3_extract')
    os.makedirs(temp_extract_level3)
    
    with zipfile.ZipFile(level3_path, 'r') as zf:
        zf.extractall(temp_extract_level3)
    
    # Modify the file
    deepfile_path = os.path.join(temp_extract_level3, 'deepfile.txt')
    with open(deepfile_path, 'w') as f:
        f.write('MODIFIED CONTENT at level 3\nThis was changed by the user')
    
    # Step 4: Simulate UpdateSingleArchive for level3
    print("\n🔄 Step 4: UpdateSingleArchive for level3...")
    new_level3_path = level3_path + '.new'
    
    with zipfile.ZipFile(new_level3_path, 'w') as zf:
        for root, dirs, files in os.walk(temp_extract_level3):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, temp_extract_level3)
                print(f"   📄 Adding to level3: {arc_name}")
                zf.write(file_path, arc_name)
    
    os.remove(level3_path)
    os.rename(new_level3_path, level3_path)
    
    # Step 5: PropagateChangesUpward - Update level2 with modified level3
    print("\n🔄 Step 5: PropagateChangesUpward - Updating level2...")
    temp_extract_level2 = os.path.join(temp_dir, 'level2_extract')
    os.makedirs(temp_extract_level2)
    
    # Extract level2 completely
    with zipfile.ZipFile(level2_path, 'r') as zf:
        for entry in zf.infolist():
            if not entry.is_dir():
                entry_path = entry.filename
                full_path = os.path.join(temp_extract_level2, entry_path)
                dir_path = os.path.dirname(full_path)
                
                if dir_path and not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                
                print(f"   📄 Extracting from level2: {entry_path}")
                
                # If this is the level3.zip, use the modified version
                if entry_path == 'nested/level3.zip':
                    with open(level3_path, 'rb') as modified_level3:
                        with open(full_path, 'wb') as target:
                            target.write(modified_level3.read())
                    print(f"   ✏️  Replaced with modified level3.zip")
                else:
                    with zf.open(entry_path) as source:
                        with open(full_path, 'wb') as target:
                            target.write(source.read())
    
    # Recreate level2 archive - THIS IS WHERE DUPLICATES MIGHT OCCUR
    print("\n🔄 Recreating level2 archive...")
    new_level2_path = level2_path + '.new'
    
    with zipfile.ZipFile(new_level2_path, 'w') as zf:
        for root, dirs, files in os.walk(temp_extract_level2):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, temp_extract_level2)
                arc_name = arc_name.replace('\\', '/')  # Normalize paths
                print(f"   📄 Adding to level2: {arc_name}")
                zf.write(file_path, arc_name)
    
    os.remove(level2_path)
    os.rename(new_level2_path, level2_path)
    
    # Step 6: PropagateChangesUpward - Update level1 with modified level2
    print("\n🔄 Step 6: PropagateChangesUpward - Updating level1...")
    temp_extract_level1 = os.path.join(temp_dir, 'level1_extract')
    os.makedirs(temp_extract_level1)
    
    # Extract level1 completely
    with zipfile.ZipFile(archive_path, 'r') as zf:
        for entry in zf.infolist():
            if not entry.is_dir():
                entry_path = entry.filename
                full_path = os.path.join(temp_extract_level1, entry_path)
                dir_path = os.path.dirname(full_path)
                
                if dir_path and not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                
                print(f"   📄 Extracting from level1: {entry_path}")
                
                # If this is the level2.zip, use the modified version
                if entry_path == 'level2.zip':
                    with open(level2_path, 'rb') as modified_level2:
                        with open(full_path, 'wb') as target:
                            target.write(modified_level2.read())
                    print(f"   ✏️  Replaced with modified level2.zip")
                else:
                    with zf.open(entry_path) as source:
                        with open(full_path, 'wb') as target:
                            target.write(source.read())
    
    # Recreate level1 archive - POTENTIAL DUPLICATE SOURCE
    print("\n🔄 Recreating level1 archive...")
    new_level1_path = archive_path + '.new'
    
    with zipfile.ZipFile(new_level1_path, 'w') as zf:
        for root, dirs, files in os.walk(temp_extract_level1):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, temp_extract_level1)
                arc_name = arc_name.replace('\\', '/')  # Normalize paths
                print(f"   📄 Adding to level1: {arc_name}")
                zf.write(file_path, arc_name)
    
    os.remove(archive_path)
    os.rename(new_level1_path, archive_path)
    
    # Clean up
    shutil.rmtree(temp_dir)
    
    # Analyze AFTER
    after_exact, after_content = detailed_analysis_with_content(archive_path, "AFTER Nested Save-Back")
    
    # Report results
    print("\n📊 DUPLICATE ANALYSIS RESULTS:")
    print("=" * 50)
    
    if after_exact and not before_exact:
        print(f"❌ NEW EXACT DUPLICATES INTRODUCED: {after_exact}")
        return False
    elif after_content and not before_content:
        print(f"⚠️  NEW CONTENT DUPLICATES INTRODUCED: {after_content}")
        return False
    elif len(after_exact) > len(before_exact) or len(after_content) > len(before_content):
        print(f"❌ MORE DUPLICATES THAN BEFORE:")
        print(f"   Before: {len(before_exact)} exact, {len(before_content)} content")
        print(f"   After:  {len(after_exact)} exact, {len(after_content)} content")
        return False
    else:
        print("✅ No new duplicates introduced by save-back process")
        return True
    
    return archive_path

def main():
    """Main test function"""
    print("🚀 Testing Real Nested Archive Duplicate Issue")
    print("=" * 60)
    
    try:
        success = simulate_nested_save_back()
        
        print(f"\n🔍 FINAL RESULT:")
        print("=" * 40)
        if success:
            print("✅ No duplicates detected in nested save-back")
        else:
            print("❌ Duplicates found! This confirms the bug exists.")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        for file in ['level1.zip']:
            if os.path.exists(file):
                os.remove(file)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 