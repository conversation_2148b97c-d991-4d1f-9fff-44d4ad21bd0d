# Nested Archive Navigator

A Windows file manager application that allows you to navigate nested archives like folders, similar to 7-Zip's File Manager feature. You can drill down into archives within archives seamlessly and even edit files inside nested archives.

## Features

✅ **Implemented:**
- Navigate nested archives like folders (7z, zip, rar, tar, gz, tgz)
- Seamless drilling down into archives within archives
- Back navigation through archive hierarchy
- File extraction and opening with default applications
- File monitoring for changes (foundation for editing)
- Clean, Windows Explorer-like interface
- Status bar with file counts and operation feedback
- Support for multiple archive formats via 7-Zip library

🚧 **Planned:**
- Archive modification (updating files back into archives)
- Drag & drop support
- Context menus
- File preview
- Better icons and theming

## Architecture

### Core Components

1. **ArchiveNavigator** - Core logic for archive navigation and file operations
2. **MainForm** - Windows Forms GUI with file list and navigation controls
3. **SevenZip Integration** - COM interfaces and wrappers for 7-Zip library
   - `SevenZipInterfaces.cs` - COM interface definitions
   - `SevenZipExtractor.cs` - High-level wrapper for archive operations
   - `ArchiveExtractCallback.cs` - Extraction callback implementation

### Key Classes

- `ArchiveNavigator` - Manages archive stack, navigation, and file operations
- `ArchiveItem` - Represents files/folders within archives
- `ArchiveLevel` - Represents a level in the navigation stack
- `FileMonitor` - Monitors extracted files for changes

## Requirements

### For Development
- .NET 6.0 or later
- Windows (for Windows Forms and 7-Zip integration)
- Visual Studio or VS Code with C# extension

### For Runtime
- Windows OS
- .NET 6.0 Runtime
- 7z.dll (from 7-Zip installation)

## Building

```bash
cd NestedArchiveNavigator
dotnet build
```

## Deployment

1. Build the project in Release mode
2. Copy `7z.dll` from your 7-Zip installation to the output directory
3. The application will look for `7z.dll` in the same directory as the executable

### Getting 7z.dll

Download 7-Zip from https://www.7-zip.org/ and copy `7z.dll` from the installation directory (usually `C:\Program Files\7-Zip\7z.dll`).

## Usage

1. Launch the application
2. Click "Open Archive" to select an archive file
3. Double-click folders or archives to navigate into them
4. Double-click files to extract and open them
5. Use the "Back" button to navigate up the hierarchy
6. The path bar shows your current location in the archive hierarchy

## Technical Notes

### 7-Zip Integration
The application uses 7-Zip's COM interfaces directly rather than the managed wrapper libraries. This provides:
- Better control over the extraction process
- Support for all 7-Zip formats
- Ability to work with nested archives
- Foundation for future archive modification features

### File Monitoring
When you open a file from an archive, the application:
1. Extracts it to a temporary location
2. Opens it with the default application
3. Monitors the file for changes
4. (Future) Updates the archive when changes are detected

### Memory Management
The application properly manages COM objects and temporary files:
- COM objects are released when no longer needed
- Temporary files are cleaned up on application exit
- Navigation stack prevents memory leaks

## Known Limitations

1. **Read-Only**: Currently only supports reading archives, not modifying them
2. **Windows Only**: Uses Windows Forms and Windows-specific 7-Zip library
3. **7z.dll Dependency**: Requires 7-Zip library to be present
4. **Temporary Files**: Extracted files remain in temp directory until app closes

## Future Enhancements

1. **Archive Modification**: Implement updating archives with modified files
2. **Better UI**: Add icons, themes, and modern styling
3. **Performance**: Lazy loading for large archives
4. **Cross-Platform**: Consider Avalonia UI for cross-platform support
5. **Plugin System**: Support for additional archive formats

## Contributing

This is a demonstration project showing how to build a 7-Zip File Manager clone. Feel free to extend it with additional features!
