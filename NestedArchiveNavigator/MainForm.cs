using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using System.Drawing;
using System.Linq;

namespace NestedArchiveNavigator
{
    public partial class MainForm : Form
    {
        private ArchiveNavigator _navigator;
        private string _currentPath = "";

        public MainForm()
        {
            InitializeComponent();
            _navigator = new ArchiveNavigator();

            // Set up event handlers
            fileListView.DoubleClick += FileListView_DoubleClick;
            backButton.Click += BackButton_Click;
            openButton.Click += OpenButton_Click;

            // Initialize icons
            InitializeIcons();

            // Set initial status
            statusLabel.Text = "Ready - Open an archive to begin";
        }

        private void InitializeIcons()
        {
            // Create simple icons using system drawing
            imageList.Images.Add("folder", SystemIcons.WinLogo.ToBitmap()); // Placeholder for folder
            imageList.Images.Add("archive", SystemIcons.WinLogo.ToBitmap()); // Placeholder for archive
            imageList.Images.Add("file", SystemIcons.Application.ToBitmap()); // Placeholder for file
        }

        private void OpenButton_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = "Archive files|*.7z;*.zip;*.rar;*.tar;*.gz;*.tgz|All files|*.*";
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    OpenArchive(dialog.FileName);
                }
            }
        }

        private void OpenArchive(string path)
        {
            try
            {
                statusLabel.Text = "Opening archive...";
                Application.DoEvents();

                _navigator.OpenArchive(path);
                _currentPath = path;
                UpdatePathDisplay();
                RefreshFileList();

                statusLabel.Text = $"Opened {Path.GetFileName(path)}";
            }
            catch (Exception ex)
            {
                statusLabel.Text = "Failed to open archive";
                MessageBox.Show($"Error opening archive: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshFileList()
        {
            fileListView.Items.Clear();

            try
            {
                var items = _navigator.GetCurrentItems().ToList();

                foreach (var item in items)
                {
                    ListViewItem lvItem = new ListViewItem(item.Name);
                    lvItem.SubItems.Add(FormatSize(item.Size));
                    lvItem.SubItems.Add(item.IsDirectory ? "Folder" :
                                       (item.IsArchive ? "Archive" : "File"));
                    lvItem.Tag = item;

                    // Set appropriate icon
                    if (item.IsDirectory)
                        lvItem.ImageKey = "folder";
                    else if (item.IsArchive)
                        lvItem.ImageKey = "archive";
                    else
                        lvItem.ImageKey = "file";

                    fileListView.Items.Add(lvItem);
                }

                // Update status
                int fileCount = items.Count(i => !i.IsDirectory);
                int folderCount = items.Count(i => i.IsDirectory);
                statusLabel.Text = $"{items.Count} items ({folderCount} folders, {fileCount} files)";
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"Error: {ex.Message}";
                MessageBox.Show($"Error refreshing file list: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FileListView_DoubleClick(object sender, EventArgs e)
        {
            if (fileListView.SelectedItems.Count == 0)
                return;

            var item = (ArchiveItem)fileListView.SelectedItems[0].Tag;

            if (item.IsDirectory || item.IsArchive)
            {
                try
                {
                    // Navigate into folder or archive
                    statusLabel.Text = "Navigating...";
                    Application.DoEvents();

                    _navigator.NavigateInto(item.Index);
                    UpdatePathDisplay();
                    RefreshFileList();
                }
                catch (Exception ex)
                {
                    statusLabel.Text = "Navigation failed";
                    MessageBox.Show($"Error navigating into {item.Name}: {ex.Message}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                // Extract and open file for viewing/editing
                try
                {
                    statusLabel.Text = "Extracting file...";
                    Application.DoEvents();

                    string tempPath = _navigator.ExtractToTemp(item.Index);
                    if (!string.IsNullOrEmpty(tempPath))
                    {
                        var startInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = tempPath,
                            UseShellExecute = true
                        };
                        System.Diagnostics.Process.Start(startInfo);

                        // Register for monitoring changes
                        _navigator.MonitorFileForChanges(item.Index, tempPath);
                        statusLabel.Text = $"Opened {item.Name}";
                    }
                    else
                    {
                        statusLabel.Text = "Failed to extract file";
                    }
                }
                catch (Exception ex)
                {
                    statusLabel.Text = "Failed to open file";
                    MessageBox.Show($"Error opening file {item.Name}: {ex.Message}", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void UpdatePathDisplay()
        {
            pathTextBox.Text = _navigator.CurrentPath;
            backButton.Enabled = _navigator.CanNavigateBack();
        }

        private void BackButton_Click(object sender, EventArgs e)
        {
            try
            {
                if (_navigator.NavigateBack())
                {
                    UpdatePathDisplay();
                    RefreshFileList();
                    statusLabel.Text = "Navigated back";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating back: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private string FormatSize(long size)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = size;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            return $"{len:0.##} {sizes[order]}";
        }
    }
}