@echo off
echo Building Nested Archive Navigator...

REM Build the project in Release mode
dotnet build -c Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!

REM Check if 7z.dll exists in the output directory
set OUTPUT_DIR=bin\Release\net6.0-windows
if not exist "%OUTPUT_DIR%\7z.dll" (
    echo.
    echo WARNING: 7z.dll not found in output directory!
    echo Please copy 7z.dll from your 7-Zip installation to:
    echo %CD%\%OUTPUT_DIR%\
    echo.
    echo You can find 7z.dll at: C:\Program Files\7-Zip\7z.dll
    echo.
)

echo.
echo Deployment complete!
echo Run the application: %OUTPUT_DIR%\NestedArchiveNavigator.exe
echo.
pause
