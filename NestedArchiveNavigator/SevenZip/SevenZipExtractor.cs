using System;
using System.IO;
using System.Runtime.InteropServices;

namespace SevenZip
{
    public class SevenZipExtractor : IDisposable
    {
        private static string _libraryPath;
        private IInArchive _archive;
        private bool _disposed = false;

        public IInArchive ArchiveInterface => _archive;

        public static void SetLibraryPath(string path)
        {
            _libraryPath = path;
        }

        public SevenZipExtractor(string archivePath)
        {
            if (string.IsNullOrEmpty(_libraryPath))
                throw new InvalidOperationException("7-Zip library path not set. Call SetLibraryPath first.");

            if (!File.Exists(_libraryPath))
                throw new FileNotFoundException($"7-Zip library not found at: {_libraryPath}");

            LoadArchive(archivePath);
        }

        private void LoadArchive(string archivePath)
        {
            try
            {
                // Load 7z.dll
                IntPtr lib = LoadLibrary(_libraryPath);
                if (lib == IntPtr.Zero)
                    throw new Exception("Failed to load 7z.dll");

                // Get CreateObject function
                IntPtr createObjectPtr = GetProcAddress(lib, "CreateObject");
                if (createObjectPtr == IntPtr.Zero)
                    throw new Exception("Failed to get CreateObject function from 7z.dll");

                // Create delegate for CreateObject
                var createObject = Marshal.GetDelegateForFunctionPointer<CreateObjectDelegate>(createObjectPtr);

                // Create archive handler
                Guid clsid = new Guid("23170F69-40C1-278A-1000-000110070000"); // 7z format
                Guid iid = typeof(IInArchive).GUID;
                
                int result = createObject(ref clsid, ref iid, out object archiveObj);
                if (result != 0 || archiveObj == null)
                    throw new Exception("Failed to create archive object");

                _archive = (IInArchive)archiveObj;

                // Open the archive file
                using (var fileStream = new FileStream(archivePath, FileMode.Open, FileAccess.Read))
                {
                    var inStream = new InStreamWrapper(fileStream);
                    ulong maxCheckStartPosition = 1 << 22; // 4MB
                    
                    result = _archive.Open(inStream, ref maxCheckStartPosition, null);
                    if (result != 0)
                        throw new Exception($"Failed to open archive: {archivePath}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading archive: {ex.Message}", ex);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                if (_archive != null)
                {
                    _archive.Close();
                    Marshal.ReleaseComObject(_archive);
                    _archive = null;
                }
                _disposed = true;
            }
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate int CreateObjectDelegate(ref Guid clsid, ref Guid iid, [MarshalAs(UnmanagedType.Interface)] out object outObject);
    }

    public class InStreamWrapper : IInStream
    {
        private Stream _stream;

        public InStreamWrapper(Stream stream)
        {
            _stream = stream;
        }

        public int Read(byte[] data, uint size, IntPtr processedSize)
        {
            try
            {
                int bytesRead = _stream.Read(data, 0, (int)size);
                if (processedSize != IntPtr.Zero)
                {
                    Marshal.WriteInt32(processedSize, bytesRead);
                }
                return 0; // S_OK
            }
            catch
            {
                return -1; // E_FAIL
            }
        }

        public int Seek(long offset, uint seekOrigin, IntPtr newPosition)
        {
            try
            {
                SeekOrigin origin = (SeekOrigin)seekOrigin;
                long pos = _stream.Seek(offset, origin);
                if (newPosition != IntPtr.Zero)
                {
                    Marshal.WriteInt64(newPosition, pos);
                }
                return 0; // S_OK
            }
            catch
            {
                return -1; // E_FAIL
            }
        }
    }
}
