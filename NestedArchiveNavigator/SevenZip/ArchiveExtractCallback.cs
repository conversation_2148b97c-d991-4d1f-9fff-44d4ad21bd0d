using System;
using System.IO;
using System.Runtime.InteropServices;

namespace SevenZip
{
    public class ArchiveExtractCallback : IArchiveExtractCallback
    {
        private Stream _outputStream;
        private ulong _total;
        private ulong _completed;

        public ArchiveExtractCallback(Stream outputStream)
        {
            _outputStream = outputStream;
        }

        public int SetTotal(ulong total)
        {
            _total = total;
            return 0; // S_OK
        }

        public int SetCompleted(ref ulong completeValue)
        {
            _completed = completeValue;
            return 0; // S_OK
        }

        public int GetStream(uint index, out ISequentialOutStream outStream, int askExtractMode)
        {
            if (askExtractMode == 0) // Extract mode
            {
                outStream = new OutStreamWrapper(_outputStream);
                return 0; // S_OK
            }
            else
            {
                outStream = null;
                return 0; // S_OK (test mode)
            }
        }

        public int PrepareOperation(int askExtractMode)
        {
            return 0; // S_OK
        }

        public int SetOperationResult(int operationResult)
        {
            return 0; // S_OK
        }
    }

    public class OutStreamWrapper : ISequentialOutStream
    {
        private Stream _stream;

        public OutStreamWrapper(Stream stream)
        {
            _stream = stream;
        }

        public int Write(byte[] data, uint size, IntPtr processedSize)
        {
            try
            {
                _stream.Write(data, 0, (int)size);
                if (processedSize != IntPtr.Zero)
                {
                    Marshal.WriteInt32(processedSize, (int)size);
                }
                return 0; // S_OK
            }
            catch
            {
                return -1; // E_FAIL
            }
        }
    }
}
