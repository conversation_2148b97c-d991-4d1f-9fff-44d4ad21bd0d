using System;
using System.Runtime.InteropServices;
using System.IO;

namespace SevenZip
{
    // 7-Zip Interface IDs
    [ComImport]
    [Guid("23170F69-40C1-278A-0000-000600600000")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IInArchive
    {
        [PreserveSig]
        int Open(IInStream stream, [In] ref ulong maxCheckStartPosition, [MarshalAs(UnmanagedType.Interface)] IArchiveOpenCallback openCallback);

        [PreserveSig]
        int Close();

        [PreserveSig]
        int GetNumberOfItems([Out] out uint numItems);

        [PreserveSig]
        int GetProperty(uint index, ItemPropId propID, ref PropVariant value);

        [PreserveSig]
        int Extract([MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 1)] uint[] indices, uint numItems, int testMode, [MarshalAs(UnmanagedType.Interface)] IArchiveExtractCallback extractCallback);

        [PreserveSig]
        int GetArchiveProperty(ArchivePropId propID, ref PropVariant value);

        [PreserveSig]
        int GetNumberOfProperties([Out] out uint numProps);

        [PreserveSig]
        int GetPropertyInfo(uint index, [MarshalAs(UnmanagedType.BStr)] out string name, out ItemPropId propID, out ushort varType);
    }

    [ComImport]
    [Guid("23170F69-40C1-278A-0000-000600400000")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IArchiveExtractCallback
    {
        [PreserveSig]
        int SetTotal(ulong total);

        [PreserveSig]
        int SetCompleted([In] ref ulong completeValue);

        [PreserveSig]
        int GetStream(uint index, [MarshalAs(UnmanagedType.Interface)] out ISequentialOutStream outStream, int askExtractMode);

        [PreserveSig]
        int PrepareOperation(int askExtractMode);

        [PreserveSig]
        int SetOperationResult(int operationResult);
    }

    [ComImport]
    [Guid("23170F69-40C1-278A-0000-000600500000")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IArchiveOpenCallback
    {
        [PreserveSig]
        int SetTotal([In] ref ulong files, [In] ref ulong bytes);

        [PreserveSig]
        int SetCompleted([In] ref ulong files, [In] ref ulong bytes);
    }

    [ComImport]
    [Guid("23170F69-40C1-278A-0000-000300030000")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface ISequentialOutStream
    {
        [PreserveSig]
        int Write([In, MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 1)] byte[] data, uint size, IntPtr processedSize);
    }

    [ComImport]
    [Guid("23170F69-40C1-278A-0000-000300020000")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IInStream
    {
        [PreserveSig]
        int Read([Out, MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 1)] byte[] data, uint size, IntPtr processedSize);

        [PreserveSig]
        int Seek(long offset, uint seekOrigin, IntPtr newPosition);
    }

    // Property IDs
    public enum ItemPropId : uint
    {
        kpidNoProperty = 0,
        kpidMainSubfile = 1,
        kpidHandlerItemIndex = 2,
        kpidPath = 3,
        kpidName = 4,
        kpidExtension = 5,
        kpidIsDir = 6,
        kpidSize = 7,
        kpidPackSize = 8,
        kpidAttrib = 9,
        kpidCTime = 10,
        kpidATime = 11,
        kpidMTime = 12,
        kpidSolid = 13,
        kpidCommented = 14,
        kpidEncrypted = 15,
        kpidSplitBefore = 16,
        kpidSplitAfter = 17,
        kpidDictionarySize = 18,
        kpidCRC = 19,
        kpidType = 20,
        kpidIsAnti = 21,
        kpidMethod = 22,
        kpidHostOS = 23,
        kpidFileSystem = 24,
        kpidUser = 25,
        kpidGroup = 26,
        kpidBlock = 27,
        kpidComment = 28,
        kpidPosition = 29,
        kpidPrefix = 30,
        kpidNumSubDirs = 31,
        kpidNumSubFiles = 32,
        kpidUnpackVer = 33,
        kpidVolume = 34,
        kpidIsVolume = 35,
        kpidOffset = 36,
        kpidLinks = 37,
        kpidNumBlocks = 38,
        kpidNumVolumes = 39,
        kpidTimeType = 40,
        kpidBit64 = 41,
        kpidBigEndian = 42,
        kpidCpu = 43,
        kpidPhySize = 44,
        kpidHeadersSize = 45,
        kpidChecksum = 46,
        kpidCharacts = 47,
        kpidVa = 48,
        kpidId = 49,
        kpidShortName = 50,
        kpidCreatorApp = 51,
        kpidSectorSize = 52,
        kpidPosixAttrib = 53,
        kpidSymLink = 54,
        kpidError = 55,
        kpidTotalSize = 0x1100,
        kpidFreeSpace = 0x1101,
        kpidClusterSize = 0x1102,
        kpidVolumeName = 0x1103
    }

    public enum ArchivePropId : uint
    {
        kpidNoProperty = 0,
        kpidHandlerItemIndex = 2,
        kpidPath = 3,
        kpidName = 4,
        kpidExtension = 5,
        kpidIsDir = 6,
        kpidSize = 7,
        kpidPackSize = 8,
        kpidAttrib = 9,
        kpidCTime = 10,
        kpidATime = 11,
        kpidMTime = 12,
        kpidSolid = 13,
        kpidCommented = 14,
        kpidEncrypted = 15,
        kpidTotalPhySize = 0x1041,
        kpidCharacts = 0x1042,
        kpidCreatorApp = 0x1043,
        kpidNumStreams = 0x1044,
        kpidNumBlocks = 0x1045
    }

    // PropVariant structure for property values
    [StructLayout(LayoutKind.Explicit)]
    public struct PropVariant
    {
        [FieldOffset(0)]
        public ushort vt;
        [FieldOffset(8)]
        public IntPtr pointerValue;
        [FieldOffset(8)]
        public byte byteValue;
        [FieldOffset(8)]
        public long longValue;
        [FieldOffset(8)]
        public ulong ulongValue;
        [FieldOffset(8)]
        public int intValue;
        [FieldOffset(8)]
        public uint uintValue;
        [FieldOffset(8)]
        public short shortValue;
        [FieldOffset(8)]
        public ushort ushortValue;
        [FieldOffset(8)]
        public sbyte sbyteValue;
        [FieldOffset(8)]
        public float floatValue;
        [FieldOffset(8)]
        public double doubleValue;

        public object Object
        {
            get
            {
                switch (vt)
                {
                    case 0: // VT_EMPTY
                        return null;
                    case 1: // VT_NULL
                        return null;
                    case 2: // VT_I2
                        return shortValue;
                    case 3: // VT_I4
                        return intValue;
                    case 4: // VT_R4
                        return floatValue;
                    case 5: // VT_R8
                        return doubleValue;
                    case 8: // VT_BSTR
                        return Marshal.PtrToStringBSTR(pointerValue);
                    case 11: // VT_BOOL
                        return shortValue != 0;
                    case 19: // VT_UI4
                        return uintValue;
                    case 20: // VT_I8
                        return longValue;
                    case 21: // VT_UI8
                        return ulongValue;
                    case 64: // VT_FILETIME
                        return DateTime.FromFileTime(longValue);
                    default:
                        return null;
                }
            }
        }
    }
}