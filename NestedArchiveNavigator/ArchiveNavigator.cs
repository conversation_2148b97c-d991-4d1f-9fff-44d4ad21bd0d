using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using SevenZip;

namespace NestedArchiveNavigator
{
    public class ArchiveNavigator
    {
        private IInArchive? _currentArchive;
        private Stack<ArchiveLevel> _navigationStack = new Stack<ArchiveLevel>();
        private Dictionary<string, FileMonitor> _monitoredFiles = new Dictionary<string, FileMonitor>();
        private string _tempFolder;

        public string CurrentPath { get; private set; } = string.Empty;
        
        public ArchiveNavigator()
        {
            // Initialize 7-Zip library
            SevenZipExtractor.SetLibraryPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "7z.dll"));
            
            // Create temp folder for extracted files
            _tempFolder = Path.Combine(Path.GetTempPath(), "NestedArchiveNavigator_" + Guid.NewGuid().ToString());
            Directory.CreateDirectory(_tempFolder);
        }
        
        ~ArchiveNavigator()
        {
            // Clean up temp files
            try
            {
                if (Directory.Exists(_tempFolder))
                    Directory.Delete(_tempFolder, true);
            }
            catch { /* Ignore cleanup errors */ }
        }
        
        public void OpenArchive(string path)
        {
            // Close any currently open archive
            if (_currentArchive != null)
            {
                Marshal.ReleaseComObject(_currentArchive);
                _currentArchive = null;
            }

            // Open the new archive
            var extractor = new SevenZipExtractor(path);
            _currentArchive = extractor.ArchiveInterface;
            CurrentPath = path;

            // Clear navigation stack
            _navigationStack.Clear();
        }
        
        public IEnumerable<ArchiveItem> GetCurrentItems()
        {
            if (_currentArchive == null)
                yield break;
                
            uint itemCount = 0;
            _currentArchive.GetNumberOfItems(out itemCount);
            
            for (uint i = 0; i < itemCount; i++)
            {
                string name = GetProperty(_currentArchive, i, ItemPropId.kpidPath) as string;
                bool isFolder = GetProperty(_currentArchive, i, ItemPropId.kpidIsDir) as bool? ?? false;
                long size = GetProperty(_currentArchive, i, ItemPropId.kpidSize) as long? ?? 0;
                
                // Determine if it's an archive based on extension
                bool isArchive = false;
                if (!isFolder)
                {
                    string ext = Path.GetExtension(name)?.ToLower();
                    isArchive = !string.IsNullOrEmpty(ext) && 
                               (ext == ".7z" || ext == ".zip" || ext == ".rar" || 
                                ext == ".tar" || ext == ".gz" || ext == ".tgz");
                }
                
                yield return new ArchiveItem
                {
                    Name = name,
                    Size = size,
                    IsDirectory = isFolder,
                    IsArchive = isArchive,
                    Index = (int)i
                };
            }
        }
        
        public void NavigateInto(int itemIndex)
        {
            if (_currentArchive == null)
                return;
                
            // Extract the archive to a temporary location
            string tempPath = ExtractToTemp(itemIndex);
            if (string.IsNullOrEmpty(tempPath))
                return;
                
            // Save current state to navigation stack
            _navigationStack.Push(new ArchiveLevel
            {
                Archive = _currentArchive,
                Path = CurrentPath
            });
            
            // Open the nested archive
            try
            {
                var extractor = new SevenZipExtractor(tempPath);
                _currentArchive = extractor.ArchiveInterface;
                CurrentPath = tempPath;
            }
            catch
            {
                // If we can't open it as an archive, go back
                NavigateBack();
                throw;
            }
        }
        
        public bool CanNavigateBack()
        {
            return _navigationStack.Count > 0;
        }

        public bool NavigateBack()
        {
            if (_navigationStack.Count == 0)
                return false;

            // Close current archive
            if (_currentArchive != null)
            {
                Marshal.ReleaseComObject(_currentArchive);
                _currentArchive = null;
            }

            // Restore previous state
            var previousLevel = _navigationStack.Pop();
            _currentArchive = previousLevel.Archive;
            CurrentPath = previousLevel.Path;

            return true;
        }
        
        public string ExtractToTemp(int itemIndex)
        {
            if (_currentArchive == null)
                return null;
                
            try
            {
                // Get item name
                string name = GetProperty(_currentArchive, (uint)itemIndex, ItemPropId.kpidPath) as string;
                if (string.IsNullOrEmpty(name))
                    name = $"Item_{itemIndex}";
                    
                // Create a unique temp file path
                string tempPath = Path.Combine(_tempFolder, name);
                
                // Ensure directory exists
                string tempDir = Path.GetDirectoryName(tempPath);
                if (!Directory.Exists(tempDir))
                    Directory.CreateDirectory(tempDir);
                
                // Extract the file
                using (FileStream outStream = new FileStream(tempPath, FileMode.Create))
                {
                    var callback = new SevenZip.ArchiveExtractCallback(outStream);
                    _currentArchive.Extract(new uint[] { (uint)itemIndex }, 1, 0, callback);
                }
                
                return tempPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting file: {ex.Message}");
                return null;
            }
        }
        
        public void MonitorFileForChanges(int itemIndex, string tempPath)
        {
            if (_currentArchive == null || string.IsNullOrEmpty(tempPath))
                return;
                
            // Create a file monitor
            var monitor = new FileMonitor(tempPath, DateTime.Now);
            
            // Store in dictionary with the key being the combination of archive path and item index
            string key = $"{CurrentPath}:{itemIndex}";
            
            if (_monitoredFiles.ContainsKey(key))
                _monitoredFiles[key] = monitor;
            else
                _monitoredFiles.Add(key, monitor);
        }
        
        public void CheckForModifiedFiles()
        {
            foreach (var kvp in _monitoredFiles.ToArray())
            {
                if (kvp.Value.HasChanged())
                {
                    // File was modified, update the archive
                    string[] parts = kvp.Key.Split(':');
                    if (parts.Length == 2)
                    {
                        string archivePath = parts[0];
                        int itemIndex = int.Parse(parts[1]);
                        
                        // TODO: Implement updating the archive with the modified file
                        // This requires using 7-Zip's IOutArchive interface
                    }
                    
                    // Remove from monitored files
                    _monitoredFiles.Remove(kvp.Key);
                }
            }
        }
        
        private object GetProperty(IInArchive archive, uint index, ItemPropId propId)
        {
            PropVariant prop = new PropVariant();
            archive.GetProperty(index, propId, ref prop);
            return prop.Object;
        }
    }
    
    public class ArchiveItem
    {
        public string Name { get; set; } = string.Empty;
        public long Size { get; set; }
        public bool IsDirectory { get; set; }
        public bool IsArchive { get; set; }
        public int Index { get; set; }
    }

    public class ArchiveLevel
    {
        public IInArchive Archive { get; set; } = null!;
        public string Path { get; set; } = string.Empty;
    }
    
    public class FileMonitor
    {
        public string FilePath { get; private set; }
        public DateTime LastWriteTime { get; private set; }
        
        public FileMonitor(string path, DateTime lastWriteTime)
        {
            FilePath = path;
            LastWriteTime = lastWriteTime;
        }
        
        public bool HasChanged()
        {
            if (!File.Exists(FilePath))
                return false;
                
            DateTime currentWriteTime = File.GetLastWriteTime(FilePath);
            return currentWriteTime > LastWriteTime;
        }
    }
}