#!/usr/bin/env python3
"""
Test script to verify save-back functionality in Archive Navigator Pro
This script tests the complete save-back workflow:
1. Create a simple test archive
2. Simulate file extraction and modification
3. Verify changes are saved back to the archive
"""

import os
import time
import zipfile
import tempfile
import shutil
from pathlib import Path

def create_test_archive():
    """Create a simple test archive for testing save-back functionality"""
    print("🔧 Creating test archive...")
    
    # Create test content
    test_content = "Original content for save-back testing\nLine 2\nLine 3"
    
    # Create test archive
    with zipfile.ZipFile('test_save_back.zip', 'w') as zf:
        zf.writestr('test_file.txt', test_content)
        zf.writestr('readme.md', '# Test Archive\nThis is a test archive for save-back functionality.')
    
    print("✅ Test archive created: test_save_back.zip")
    return 'test_save_back.zip'

def verify_archive_content(archive_path, expected_files):
    """Verify the content of an archive"""
    print(f"🔍 Verifying archive content: {archive_path}")
    
    with zipfile.ZipFile(archive_path, 'r') as zf:
        files_in_archive = zf.namelist()
        print(f"   Files in archive: {files_in_archive}")
        
        for expected_file in expected_files:
            if expected_file in files_in_archive:
                content = zf.read(expected_file).decode('utf-8')
                print(f"   ✅ {expected_file}: {len(content)} characters")
                print(f"      Content preview: {content[:50]}...")
            else:
                print(f"   ❌ {expected_file}: NOT FOUND")
                return False
    
    return True

def simulate_file_modification():
    """Simulate the file modification process that Archive Navigator Pro would do"""
    print("🔄 Simulating file modification process...")
    
    archive_path = 'test_save_back.zip'
    
    # Step 1: Extract file to temp (like Archive Navigator Pro does)
    temp_dir = tempfile.mkdtemp(prefix='ArchiveNavigator_Test_')
    temp_file = os.path.join(temp_dir, 'test_file.txt')
    
    print(f"   📁 Temp directory: {temp_dir}")
    
    with zipfile.ZipFile(archive_path, 'r') as zf:
        with zf.open('test_file.txt') as source:
            with open(temp_file, 'wb') as target:
                target.write(source.read())
    
    print(f"   📄 Extracted to: {temp_file}")
    
    # Step 2: Modify the file (like user editing in text editor)
    original_content = open(temp_file, 'r').read()
    print(f"   📖 Original content: {original_content[:50]}...")
    
    modified_content = original_content + "\n\n--- MODIFIED BY TEST ---\nThis line was added to test save-back functionality!"
    
    with open(temp_file, 'w') as f:
        f.write(modified_content)
    
    print(f"   ✏️  Modified content: {len(modified_content)} characters")
    
    # Step 3: Simulate Archive Navigator Pro's save-back process
    print("   🔄 Simulating save-back process...")
    
    # Create backup (like Archive Navigator Pro does)
    backup_path = archive_path + '.backup_test'
    shutil.copy2(archive_path, backup_path)
    print(f"   💾 Backup created: {backup_path}")
    
    # Create updated archive (like Archive Navigator Pro does)
    temp_archive = archive_path + '.temp_test'
    
    with zipfile.ZipFile(archive_path, 'r') as original:
        with zipfile.ZipFile(temp_archive, 'w') as updated:
            for item in original.infolist():
                if item.filename == 'test_file.txt':
                    # Replace with modified file
                    print(f"   🔄 Replacing: {item.filename}")
                    updated.write(temp_file, item.filename)
                else:
                    # Copy existing file
                    print(f"   📋 Copying: {item.filename}")
                    data = original.read(item.filename)
                    updated.writestr(item, data)
    
    # Replace original with updated archive
    os.remove(archive_path)
    os.rename(temp_archive, archive_path)
    print(f"   ✅ Archive updated successfully")
    
    # Clean up
    shutil.rmtree(temp_dir)
    os.remove(backup_path)
    
    return modified_content

def test_save_back_functionality():
    """Main test function"""
    print("🚀 Testing Archive Navigator Pro Save-Back Functionality")
    print("=" * 60)
    
    try:
        # Step 1: Create test archive
        archive_path = create_test_archive()
        
        # Step 2: Verify initial content
        print("\n📋 Initial archive verification:")
        if not verify_archive_content(archive_path, ['test_file.txt', 'readme.md']):
            print("❌ Initial archive verification failed")
            return False
        
        # Step 3: Simulate modification and save-back
        print("\n🔄 Testing save-back process:")
        modified_content = simulate_file_modification()
        
        # Step 4: Verify changes were saved
        print("\n✅ Final verification:")
        with zipfile.ZipFile(archive_path, 'r') as zf:
            saved_content = zf.read('test_file.txt').decode('utf-8')
            
            if "--- MODIFIED BY TEST ---" in saved_content:
                print("   ✅ Save-back successful! Modified content found in archive.")
                print(f"   📊 Content length: {len(saved_content)} characters")
                print(f"   📝 Content preview: {saved_content[-100:]}")
                return True
            else:
                print("   ❌ Save-back failed! Modified content not found in archive.")
                return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Clean up
        if os.path.exists('test_save_back.zip'):
            os.remove('test_save_back.zip')

if __name__ == "__main__":
    success = test_save_back_functionality()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SAVE-BACK FUNCTIONALITY TEST: PASSED")
        print("✅ Archive Navigator Pro save-back mechanism works correctly!")
    else:
        print("💥 SAVE-BACK FUNCTIONALITY TEST: FAILED")
        print("❌ Save-back mechanism needs debugging")
    
    exit(0 if success else 1)
