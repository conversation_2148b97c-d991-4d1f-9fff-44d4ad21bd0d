#!/usr/bin/env python3

import os
import shutil
import subprocess
import tempfile
import zipfile
import tarfile
from pathlib import Path

def create_test_archive_with_mixed_case():
    """Create a test archive with mixed case filenames"""
    temp_dir = tempfile.mkdtemp()
    archive_path = os.path.join(temp_dir, "CaseSensitiveTest.tar")
    
    # Create test files with mixed case
    test_files = {
        "README.TXT": "This is a README file in uppercase",
        "Config.INI": "This is a config file with mixed case",
        "data.json": "This is a data file in lowercase",
        "Scripts/Deploy.SH": "This is a deploy script",
        "Scripts/backup.sh": "This is a backup script"
    }
    
    # Create temporary files
    file_paths = []
    for filename, content in test_files.items():
        file_path = os.path.join(temp_dir, filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w') as f:
            f.write(content)
        file_paths.append((filename, file_path))
    
    # Create TAR archive
    with tarfile.open(archive_path, 'w') as tar:
        for filename, file_path in file_paths:
            tar.add(file_path, arcname=filename)
    
    print(f"Created test archive: {archive_path}")
    print("Original files:")
    with tarfile.open(archive_path, 'r') as tar:
        for member in tar.getmembers():
            print(f"  - {member.name}")
    
    return archive_path

def verify_case_preservation():
    """Test that case is preserved after archive update"""
    print("\n=== Testing Case Preservation ===")
    
    # Create test archive
    original_archive = create_test_archive_with_mixed_case()
    
    print(f"\nTesting with archive: {original_archive}")
    
    # Copy archive for testing
    test_archive = original_archive.replace(".tar", "_updated.tar")
    shutil.copy2(original_archive, test_archive)
    
    print(f"Copied to: {test_archive}")
    
    # List contents before update
    print("\nContents before update:")
    with tarfile.open(test_archive, 'r') as tar:
        original_names = [member.name for member in tar.getmembers()]
        for name in sorted(original_names):
            print(f"  - {name}")
    
    # Simulate an archive update (this would be done by the C# application)
    print("\nSimulating archive update (would be done by NestedArchiveNavigator)...")
    print("Expected behavior:")
    print("  - Case should be preserved for existing files")
    print("  - No duplicate entries should be created")
    print("  - Only path separators should be normalized (/ vs \\)")
    
    return test_archive

if __name__ == "__main__":
    print("Case Preservation and Duplicate Prevention Test")
    print("=" * 50)
    
    test_archive = verify_case_preservation()
    
    print(f"\nTest archive created: {test_archive}")
    print("\nTo test with NestedArchiveNavigator:")
    print("1. Open the test archive in the application")
    print("2. Navigate to any text file (README.TXT, Config.INI, etc.)")
    print("3. Edit and save the file")
    print("4. Check that:")
    print("   - Original case is preserved (README.TXT stays uppercase)")
    print("   - No duplicate entries are created")
    print("   - File contents are updated correctly") 