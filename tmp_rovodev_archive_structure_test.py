#!/usr/bin/env python3
"""
Test script to analyze archive structure before and after save-back operations
to identify where duplicate archives are being introduced
"""

import os
import time
import zipfile
import tempfile
import shutil
from pathlib import Path

def analyze_archive_structure(archive_path, level=0, prefix=""):
    """Recursively analyze and print archive structure"""
    print(f"{prefix}📦 {os.path.basename(archive_path)} (Level {level})")
    
    try:
        with zipfile.ZipFile(archive_path, 'r') as zf:
            entries = zf.namelist()
            print(f"{prefix}   📋 Total entries: {len(entries)}")
            
            for i, entry in enumerate(entries):
                entry_type = "📁 DIR" if entry.endswith('/') else "📄 FILE"
                size = zf.getinfo(entry).file_size if not entry.endswith('/') else 0
                print(f"{prefix}   {i+1:2d}. {entry_type} {entry} ({size} bytes)")
                
                # If this is a nested archive, analyze it too
                if entry.lower().endswith(('.zip', '.tar', '.tgz', '.7z')) and not entry.endswith('/'):
                    try:
                        # Extract nested archive to temp location
                        temp_dir = tempfile.mkdtemp(prefix='ArchiveAnalysis_')
                        nested_path = os.path.join(temp_dir, entry)
                        
                        with zf.open(entry) as nested_data:
                            with open(nested_path, 'wb') as f:
                                f.write(nested_data.read())
                        
                        # Recursively analyze nested archive
                        analyze_archive_structure(nested_path, level + 1, prefix + "    ")
                        
                        # Clean up
                        shutil.rmtree(temp_dir)
                        
                    except Exception as ex:
                        print(f"{prefix}       ❌ Could not analyze nested archive: {ex}")
            
    except Exception as ex:
        print(f"{prefix}   ❌ Error reading archive: {ex}")

def create_test_nested_archive():
    """Create a test nested archive to analyze structure changes"""
    print("🔧 Creating test nested archive for structure analysis...")
    
    # Create inner content
    inner_content = "Original content for structure test\nLine 2\nLine 3"
    
    # Step 1: Create inner archive
    with zipfile.ZipFile('inner_structure_test.zip', 'w') as inner_zip:
        inner_zip.writestr('test_file.txt', inner_content)
        inner_zip.writestr('config.ini', '[Settings]\nversion=1.0')
    
    # Step 2: Create middle archive containing inner archive
    with zipfile.ZipFile('middle_structure_test.zip', 'w') as middle_zip:
        middle_zip.write('inner_structure_test.zip', 'inner_structure_test.zip')
        middle_zip.writestr('readme.txt', 'Middle level readme')
    
    # Step 3: Create outer archive containing middle archive
    with zipfile.ZipFile('nested_structure_test.zip', 'w') as outer_zip:
        outer_zip.write('middle_structure_test.zip', 'middle_structure_test.zip')
        outer_zip.writestr('outer_readme.txt', 'Outer level readme')
    
    # Clean up temp files
    os.remove('inner_structure_test.zip')
    os.remove('middle_structure_test.zip')
    
    print("✅ Created nested_structure_test.zip")
    return 'nested_structure_test.zip'

def simulate_save_back_with_structure_check():
    """Simulate save-back operation and check for structure changes"""
    print("🔄 Testing save-back operation with structure analysis...")
    
    archive_path = create_test_nested_archive()
    
    print("\n" + "="*60)
    print("📋 BEFORE SAVE-BACK - Archive Structure:")
    print("="*60)
    analyze_archive_structure(archive_path)
    
    # Simulate the save-back process that might introduce duplicates
    print("\n🔄 Simulating save-back operation...")
    
    try:
        # Step 1: Extract outer archive
        temp_dir = tempfile.mkdtemp(prefix='SaveBackTest_')
        print(f"   📁 Temp directory: {temp_dir}")
        
        with zipfile.ZipFile(archive_path, 'r') as outer_zip:
            outer_zip.extractall(temp_dir)
        
        middle_zip_path = os.path.join(temp_dir, 'middle_structure_test.zip')
        
        # Step 2: Extract middle archive
        middle_temp_dir = tempfile.mkdtemp(prefix='MiddleTest_')
        
        with zipfile.ZipFile(middle_zip_path, 'r') as middle_zip:
            middle_zip.extractall(middle_temp_dir)
        
        inner_zip_path = os.path.join(middle_temp_dir, 'inner_structure_test.zip')
        
        # Step 3: Extract inner archive and modify file
        inner_temp_dir = tempfile.mkdtemp(prefix='InnerTest_')
        
        with zipfile.ZipFile(inner_zip_path, 'r') as inner_zip:
            inner_zip.extractall(inner_temp_dir)
        
        # Modify the test file
        test_file_path = os.path.join(inner_temp_dir, 'test_file.txt')
        with open(test_file_path, 'a') as f:
            f.write("\n\n--- MODIFIED FOR STRUCTURE TEST ---")
        
        print("   ✏️  Modified test file")
        
        # Step 4: Recreate inner archive (POTENTIAL ISSUE: Check for duplicates here)
        new_inner_zip_path = inner_zip_path + '.new'
        
        with zipfile.ZipFile(new_inner_zip_path, 'w') as new_inner_zip:
            # Add all files from inner temp dir
            for root, dirs, files in os.walk(inner_temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, inner_temp_dir)
                    new_inner_zip.write(file_path, arc_name)
                    print(f"   📄 Added to inner: {arc_name}")
        
        # Replace old inner archive
        os.remove(inner_zip_path)
        os.rename(new_inner_zip_path, inner_zip_path)
        
        # Step 5: Recreate middle archive (POTENTIAL ISSUE: Check for duplicates here)
        new_middle_zip_path = middle_zip_path + '.new'
        
        with zipfile.ZipFile(new_middle_zip_path, 'w') as new_middle_zip:
            # Add all files from middle temp dir
            for root, dirs, files in os.walk(middle_temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, middle_temp_dir)
                    new_middle_zip.write(file_path, arc_name)
                    print(f"   📄 Added to middle: {arc_name}")
        
        # Replace old middle archive
        os.remove(middle_zip_path)
        os.rename(new_middle_zip_path, middle_zip_path)
        
        # Step 6: Recreate outer archive (POTENTIAL ISSUE: Check for duplicates here)
        new_outer_zip_path = archive_path + '.new'
        
        with zipfile.ZipFile(new_outer_zip_path, 'w') as new_outer_zip:
            # Add all files from temp dir
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, temp_dir)
                    new_outer_zip.write(file_path, arc_name)
                    print(f"   📄 Added to outer: {arc_name}")
        
        # Replace old outer archive
        os.remove(archive_path)
        os.rename(new_outer_zip_path, archive_path)
        
        # Clean up temp directories
        shutil.rmtree(temp_dir)
        shutil.rmtree(middle_temp_dir)
        shutil.rmtree(inner_temp_dir)
        
        print("\n" + "="*60)
        print("📋 AFTER SAVE-BACK - Archive Structure:")
        print("="*60)
        analyze_archive_structure(archive_path)
        
        print("\n" + "="*60)
        print("🔍 STRUCTURE COMPARISON ANALYSIS:")
        print("="*60)
        print("✅ Check the above structures for:")
        print("   1. Duplicate entries at any level")
        print("   2. Missing entries that should be preserved")
        print("   3. Incorrect nesting or file paths")
        print("   4. Extra files that shouldn't be there")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Clean up
        if os.path.exists(archive_path):
            os.remove(archive_path)

if __name__ == "__main__":
    print("🚀 Archive Structure Analysis - Before/After Save-Back")
    print("=" * 60)
    
    success = simulate_save_back_with_structure_check()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 STRUCTURE ANALYSIS COMPLETE")
        print("📋 Review the before/after structures above to identify duplicate issues")
    else:
        print("💥 STRUCTURE ANALYSIS FAILED")
    
    exit(0 if success else 1)