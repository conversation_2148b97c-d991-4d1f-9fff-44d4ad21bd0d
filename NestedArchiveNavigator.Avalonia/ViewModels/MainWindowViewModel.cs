using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using NestedArchiveNavigator.Avalonia.Core;
using System.Diagnostics;
using Avalonia.Platform.Storage;
using Avalonia.Threading;
using Avalonia.Controls;
using System.Timers;

namespace NestedArchiveNavigator.Avalonia.ViewModels
{
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly CrossPlatformArchiveNavigator _navigator;
        private Func<Task<string?>>? _filePickerProvider;

        [ObservableProperty]
        private string _currentPath = "Ready - Open an archive to begin";

        [ObservableProperty]
        private string _statusText = "Ready";

        [ObservableProperty]
        private bool _canNavigateBack = false;

        [ObservableProperty]
        private bool _canNavigateForward = false;

        [ObservableProperty]
        private ObservableCollection<ArchiveItemViewModel> _items = new();

        [ObservableProperty]
        private ArchiveItemViewModel? _selectedItem;

        public ICommand CheckForModificationsCommand { get; }

        public MainWindowViewModel()
        {
            _navigator = new CrossPlatformArchiveNavigator();
            _navigator.NavigationChanged += OnNavigationChanged;

            CheckForModificationsCommand = new RelayCommand(CheckForModifications);
            
            // Start automatic file monitoring
            StartFileMonitoring();
        }

        public void SetFilePickerProvider(Func<Task<string?>> provider)
        {
            _filePickerProvider = provider;
        }

        [RelayCommand]
        private async Task OpenArchive()
        {
            try
            {
                if (_filePickerProvider == null)
                {
                    StatusText = "File picker not available";
                    return;
                }

                var filePath = await _filePickerProvider();
                if (!string.IsNullOrEmpty(filePath))
                {
                    await OpenArchiveFile(filePath);
                }
            }
            catch (Exception ex)
            {
                StatusText = $"Error: {ex.Message}";
            }
        }

        public async Task OpenArchiveFile(string path)
        {
            try
            {
                Console.WriteLine($"DEBUG: Opening archive: {path}");
                StatusText = "Opening archive...";

                await Task.Run(() => _navigator.OpenArchive(path));

                // Update UI on the UI thread
                CurrentPath = _navigator.LogicalPath;
                Console.WriteLine($"DEBUG: Archive opened, calling RefreshFileList");
                RefreshFileList();
                StatusText = $"Opened {Path.GetFileName(path)}";
                Console.WriteLine($"DEBUG: Archive opening completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: Error opening archive: {ex}");
                StatusText = $"Error opening archive: {ex.Message}";
            }
        }

        [RelayCommand]
        private void NavigateBack()
        {
            try
            {
                if (_navigator.NavigateBack())
                {
                    CurrentPath = _navigator.LogicalPath;
                    RefreshFileList();
                    StatusText = "Navigated back";
                }
            }
            catch (Exception ex)
            {
                StatusText = $"Error navigating back: {ex.Message}";
            }
        }

        [RelayCommand]
        private void NavigateForward()
        {
            try
            {
                if (_navigator.NavigateForward())
                {
                    CurrentPath = _navigator.LogicalPath;
                    RefreshFileList();
                    StatusText = "Navigated forward";
                }
            }
            catch (Exception ex)
            {
                StatusText = $"Error navigating forward: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task ItemDoubleClick(ArchiveItemViewModel? item)
        {
            if (item == null) return;

            try
            {
                Console.WriteLine($"DEBUG: Double-clicking item: {item.Name}, IsDirectory: {item.IsDirectory}, IsArchive: {item.IsArchive}");

                if (item.IsDirectory || item.IsArchive)
                {
                    StatusText = "Navigating...";

                    await Task.Run(() => _navigator.NavigateInto(item.ToArchiveItem()));

                    CurrentPath = _navigator.LogicalPath;
                    RefreshFileList();
                    StatusText = $"Navigated into {item.Name}";
                }
                else
                {
                    // For text files, open in built-in editor; for others, use system default
                    if (IsTextFile(item.Name))
                    {
                        await OpenInBuiltInEditor(item);
                    }
                    else
                    {
                        await OpenWithSystemDefault(item);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: Error in ItemDoubleClick: {ex}");
                StatusText = $"Error: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task EditFile(ArchiveItemViewModel? item)
        {
            if (item == null || item.IsDirectory || item.IsArchive) return;
            await OpenInBuiltInEditor(item);
        }

        private async Task OpenInBuiltInEditor(ArchiveItemViewModel item)
        {
            try
            {
                StatusText = "Extracting file for editing...";

                string tempPath = await Task.Run(() => _navigator.ExtractToTemp(item.ToArchiveItem()));

                if (!string.IsNullOrEmpty(tempPath))
                {
                    try
                    {
                        // Create editor window as a proper modal dialog
                        var editorWindow = new NestedArchiveNavigator.Avalonia.Views.TextEditorWindow();
                        Console.WriteLine($"DEBUG: Created TextEditor window for {item.Name}");
                        
                        // Load file content
                        await editorWindow.LoadFile(tempPath, item, SaveFileBackToArchive);
                        Console.WriteLine($"DEBUG: Loaded file content into TextEditor");
                        
                        // Set window properties for better visibility on macOS
                        editorWindow.WindowState = WindowState.Normal;
                        editorWindow.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                        editorWindow.ShowInTaskbar = true;
                        editorWindow.Topmost = false; // Don't make it topmost initially
                        
                        // Use ShowDialog instead of Show for better modal behavior
                        Console.WriteLine($"DEBUG: Opening TextEditor as modal dialog");
                        
                        // Run on UI thread to ensure proper window management
                        await Dispatcher.UIThread.InvokeAsync(async () =>
                        {
                            // Get the main window as parent
                            var mainWindow = global::Avalonia.Application.Current?.ApplicationLifetime is
                                global::Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                                ? desktop.MainWindow : null;
                                
                            if (mainWindow != null)
                            {
                                Console.WriteLine($"DEBUG: Setting main window as parent");
                                await editorWindow.ShowDialog(mainWindow);
                            }
                            else
                            {
                                Console.WriteLine($"DEBUG: No main window found, using Show()");
                                editorWindow.Show();
                                editorWindow.Activate();
                                editorWindow.BringIntoView();
                            }
                        });
                        
                        Console.WriteLine($"DEBUG: TextEditor dialog completed for {item.Name}");
                        StatusText = $"Finished editing {item.Name}";
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"ERROR: Failed to open TextEditor window: {ex.Message}");
                        Console.WriteLine($"ERROR: Stack trace: {ex.StackTrace}");
                        StatusText = $"Error opening editor: {ex.Message}";
                    }
                }
                else
                {
                    StatusText = "Failed to extract file for editing";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: Error opening file in editor: {ex}");
                StatusText = $"Error opening file: {ex.Message}";
            }
        }

        private async Task OpenWithSystemDefault(ArchiveItemViewModel item)
        {
            try
            {
                StatusText = "Extracting file...";

                string tempPath = await Task.Run(() => _navigator.ExtractToTemp(item.ToArchiveItem()));

                if (!string.IsNullOrEmpty(tempPath))
                {
                    // Open file with default application
                    var startInfo = new ProcessStartInfo
                    {
                        FileName = tempPath,
                        UseShellExecute = true
                    };
                    Process.Start(startInfo);

                    // Register for monitoring changes
                    _navigator.MonitorFileForChanges(item.ToArchiveItem(), tempPath);
                    StatusText = $"Opened {item.Name}";
                }
                else
                {
                    StatusText = "Failed to extract file";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: Error opening file with system default: {ex}");
                StatusText = $"Error opening file: {ex.Message}";
            }
        }

        private async Task SaveFileBackToArchive(ArchiveItemViewModel item, string tempFilePath)
        {
            try
            {
                StatusText = "Saving changes to archive...";
                Console.WriteLine($"DEBUG: Saving file back to archive: {item.Name}");

                await _navigator.UpdateFileInArchive(item.FullPath, tempFilePath);
                
                StatusText = "File saved successfully";
                Console.WriteLine($"DEBUG: File saved back to archive successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: Error saving file back to archive: {ex}");
                StatusText = $"Error saving file: {ex.Message}";
            }
        }

        private bool IsTextFile(string fileName)
        {
            var ext = Path.GetExtension(fileName)?.ToLower();
            return !string.IsNullOrEmpty(ext) && 
                   (ext == ".txt" || ext == ".ini" || ext == ".cfg" || ext == ".conf" || 
                    ext == ".log" || ext == ".md" || ext == ".json" || ext == ".xml" || 
                    ext == ".yml" || ext == ".yaml" || ext == ".csv" || ext == ".bat" || 
                    ext == ".sh" || ext == ".py" || ext == ".js" || ext == ".css" || 
                    ext == ".html" || ext == ".htm" || ext == ".sql" || ext == ".c" || 
                    ext == ".cpp" || ext == ".h" || ext == ".cs" || ext == ".java");
        }

        private void OnNavigationChanged(object? sender, EventArgs e)
        {
            Dispatcher.UIThread.Post(() =>
            {
                CanNavigateBack = _navigator.CanNavigateBack();
                CanNavigateForward = _navigator.CanNavigateForward();
                CurrentPath = _navigator.LogicalPath;
                RefreshFileList();
            });
        }

        private void RefreshFileList()
        {
            try
            {
                Console.WriteLine("DEBUG: RefreshFileList called");
                var items = _navigator.GetCurrentItems().ToList();
                Console.WriteLine($"DEBUG: Got {items.Count} items from navigator");

                Items.Clear();
                Console.WriteLine($"DEBUG: Items collection cleared, count: {Items.Count}");

                foreach (var item in items)
                {
                    Console.WriteLine($"DEBUG: Adding item: {item.Name} (Type: {(item.IsDirectory ? "Dir" : "File")})");
                    var viewModel = new ArchiveItemViewModel(item);
                    Items.Add(viewModel);
                    Console.WriteLine($"DEBUG: Items collection count after add: {Items.Count}");
                }

                Console.WriteLine($"DEBUG: Final Items collection count: {Items.Count}");

                // Update status
                int fileCount = items.Count(i => !i.IsDirectory);
                int folderCount = items.Count(i => i.IsDirectory);
                StatusText = $"{items.Count} items ({folderCount} folders, {fileCount} files)";
                Console.WriteLine($"DEBUG: Status updated: {StatusText}");

                // Force property change notification
                OnPropertyChanged(nameof(Items));
                Console.WriteLine("DEBUG: Property change notification sent for Items");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: Error in RefreshFileList: {ex}");
                StatusText = $"Error: {ex.Message}";
            }
        }

        private string FormatSize(long size)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = size;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            return $"{len:0.##} {sizes[order]}";
        }

        private void CheckForModifications()
        {
            _navigator.CheckForModifiedFiles();
        }

        // Add automatic file monitoring
        private System.Timers.Timer? _fileMonitorTimer;

        private void StartFileMonitoring()
        {
            _fileMonitorTimer = new System.Timers.Timer(2000); // Check every 2 seconds
            _fileMonitorTimer.Elapsed += (sender, e) => CheckForModifications();
            _fileMonitorTimer.AutoReset = true;
            _fileMonitorTimer.Start();
        }

        private void StopFileMonitoring()
        {
            _fileMonitorTimer?.Stop();
            _fileMonitorTimer?.Dispose();
            _fileMonitorTimer = null;
        }
    }

    public partial class ArchiveItemViewModel : ObservableObject
    {
        [ObservableProperty]
        private string _name = string.Empty;

        [ObservableProperty]
        private string _size = string.Empty;

        [ObservableProperty]
        private string _type = string.Empty;

        [ObservableProperty]
        private string _icon = string.Empty;

        [ObservableProperty]
        private bool _isDirectory;

        [ObservableProperty]
        private bool _isArchive;

        public int Index { get; set; }
        public string FullPath { get; set; } = string.Empty;

        public ArchiveItemViewModel(ArchiveItem item)
        {
            Name = item.Name;
            Size = FormatSize(item.Size);
            Type = item.IsDirectory ? "Folder" : (item.IsArchive ? "Archive" : "File");
            Icon = item.IsDirectory ? "📁" : (item.IsArchive ? "📦" : "📄");
            IsDirectory = item.IsDirectory;
            IsArchive = item.IsArchive;
            Index = item.Index;
            FullPath = item.FullPath;

            Console.WriteLine($"DEBUG: ArchiveItemViewModel created - Name: {Name}, IsArchive: {IsArchive}, Type: {Type}, Icon: {Icon}");
        }

        public ArchiveItem ToArchiveItem()
        {
            return new ArchiveItem
            {
                Name = Name,
                Size = ParseSize(Size),
                IsDirectory = IsDirectory,
                IsArchive = IsArchive,
                Index = Index,
                FullPath = FullPath
            };
        }

        private long ParseSize(string sizeStr)
        {
            // Simple parsing - in real implementation you'd want more robust parsing
            if (string.IsNullOrEmpty(sizeStr) || sizeStr == "0 B")
                return 0;
            
            var parts = sizeStr.Split(' ');
            if (parts.Length != 2 || !double.TryParse(parts[0], out double value))
                return 0;
            
            return parts[1] switch
            {
                "KB" => (long)(value * 1024),
                "MB" => (long)(value * 1024 * 1024),
                "GB" => (long)(value * 1024 * 1024 * 1024),
                _ => (long)value
            };
        }

        private string FormatSize(long size)
        {
            if (size == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = size;
            int order = 0;
            
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
