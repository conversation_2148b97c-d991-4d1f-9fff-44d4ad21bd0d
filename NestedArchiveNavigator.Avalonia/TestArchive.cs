using System;
using System.Linq;
using SharpCompress.Archives;

namespace NestedArchiveNavigator.Avalonia
{
    public static class TestArchive
    {
        public static void TestSharpCompress(string archivePath)
        {
            try
            {
                Console.WriteLine($"Testing archive: {archivePath}");
                
                using var archive = ArchiveFactory.Open(archivePath);
                Console.WriteLine($"Archive opened successfully. Entry count: {archive.Entries.Count()}");
                
                foreach (var entry in archive.Entries)
                {
                    Console.WriteLine($"Entry: '{entry.Key}', IsDirectory: {entry.IsDirectory}, Size: {entry.Size}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
