using SharpCompress.Archives;
using SharpCompress.Common;
using SharpCompress.Writers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace NestedArchiveNavigator.Avalonia.Core
{
    public class ArchiveUpdateService
    {
        public void UpdateArchive(string archivePath, IEnumerable<(string entryPath, byte[] newContent)> updates)
        {
            var tempFilePath = Path.GetTempFileName();
            var archiveType = GetArchiveType(archivePath);
            var updateList = updates.ToList();

            // DUPLICATE FIX: Normalize all paths to prevent comparison issues
            var normalizedUpdates = updateList.Select(u => (
                entryPath: NormalizePath(u.entryPath),
                newContent: u.newContent
            )).ToList();

            // DUPLICATE FIX: Create sets for faster duplicate checking using case-insensitive comparison
            var updatePaths = new HashSet<string>(normalizedUpdates.Select(u => GetComparisonKey(u.entryPath)), StringComparer.OrdinalIgnoreCase);
            var processedEntries = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            Console.WriteLine($"DEBUG: ArchiveUpdateService - Updating {normalizedUpdates.Count} entries in {Path.GetFileName(archivePath)}");
            foreach (var update in normalizedUpdates)
            {
                Console.WriteLine($"DEBUG: - Will update: '{update.entryPath}' ({update.newContent.Length} bytes)");
            }

            using (var originalArchiveStream = File.OpenRead(archivePath))
            using (var tempStream = File.OpenWrite(tempFilePath))
            {
                using (var originalArchive = ArchiveFactory.Open(originalArchiveStream))
                using (var writer = WriterFactory.Open(tempStream, archiveType, GetWriterOptions(archiveType)))
                {
                    // DUPLICATE FIX: Write the updated files first
                    foreach (var update in normalizedUpdates)
                    {
                        Console.WriteLine($"DEBUG: Writing updated entry: '{update.entryPath}'");
                        processedEntries.Add(GetComparisonKey(update.entryPath)); // Track updated entries using comparison key
                        using (var contentStream = new MemoryStream(update.newContent))
                        {
                            if (archiveType == ArchiveType.Tar)
                            {
                                // TAR requires explicit size
                                ((SharpCompress.Writers.Tar.TarWriter)writer).Write(update.entryPath, contentStream, null, update.newContent.Length);
                            }
                            else
                            {
                                writer.Write(update.entryPath, contentStream);
                            }
                        }
                    }

                    // DUPLICATE FIX: Copy the rest of the files from the original archive with proper duplicate checking
                    
                    foreach (var entry in originalArchive.Entries)
                    {
                        var entryKey = entry.Key ?? "";
                        var normalizedEntryKey = NormalizePath(entryKey);
                        var comparisonKey = GetComparisonKey(entryKey);
                        
                        Console.WriteLine($"DEBUG: Processing entry: '{entryKey}' -> normalized: '{normalizedEntryKey}' -> comparison: '{comparisonKey}'");
                        
                        // Skip directories and updated entries
                        if (entry.IsDirectory)
                        {
                            Console.WriteLine($"DEBUG: Skipping directory: '{entryKey}'");
                            continue;
                        }
                        
                        // CRITICAL FIX: Check if this entry was already processed (prevents duplicates)
                        if (processedEntries.Contains(comparisonKey))
                        {
                            Console.WriteLine($"DEBUG: Skipping already processed entry: '{entryKey}' (comparison: '{comparisonKey}')");
                            continue;
                        }
                        
                        // CRITICAL FIX: Use comparison key for duplicate checking to prevent duplicates
                        if (updatePaths.Contains(comparisonKey))
                        {
                            Console.WriteLine($"DEBUG: Skipping updated entry: '{entryKey}' (comparison: '{comparisonKey}')");
                            Console.WriteLine($"DEBUG: UpdatePaths contains: [{string.Join(", ", updatePaths)}]");
                            continue;
                        }
                        
                        if (!string.IsNullOrEmpty(entryKey))
                        {
                            Console.WriteLine($"DEBUG: Copying existing entry: '{entryKey}' (preserving original case)");
                            processedEntries.Add(comparisonKey); // Track using comparison key
                            
                            if (archiveType == ArchiveType.Tar)
                            {
                                // TAR requires explicit size for existing entries too
                                using (var entryStream = entry.OpenEntryStream())
                                {
                                    // Use original entryKey to preserve case
                                    ((SharpCompress.Writers.Tar.TarWriter)writer).Write(entryKey, entryStream, null, entry.Size);
                                }
                            }
                            else
                            {
                                // Use original entryKey to preserve case
                                writer.Write(entryKey, entry.OpenEntryStream());
                            }
                        }
                        else
                        {
                            Console.WriteLine($"DEBUG: Skipping entry with null/empty key");
                        }
                    }
                }
            }
            
            Console.WriteLine($"DEBUG: ArchiveUpdateService - Archive update summary:");
            Console.WriteLine($"DEBUG: - Updated entries: {normalizedUpdates.Count}");
            Console.WriteLine($"DEBUG: - Processed existing entries: {processedEntries.Count}");
            Console.WriteLine($"DEBUG: - Total entries in new archive: {normalizedUpdates.Count + processedEntries.Count}");
            Console.WriteLine($"DEBUG: ArchiveUpdateService - Successfully created updated archive");
            File.Move(tempFilePath, archivePath, true);
        }
        
        private string NormalizePath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return "";
                
            // Normalize path separators to forward slashes and trim
            var normalized = path.Replace('\\', '/').Trim();
            
            // Remove leading/trailing slashes
            normalized = normalized.Trim('/');
            
            // DO NOT convert to lowercase - preserve original case!
            // Only normalize separators for consistent comparison
            
            Console.WriteLine($"DEBUG: NormalizePath: '{path}' -> '{normalized}'");
            return normalized;
        }
        
        // Helper method for case-insensitive comparison without changing the actual path
        private string GetComparisonKey(string path)
        {
            return NormalizePath(path).ToLowerInvariant();
        }

        private ArchiveType GetArchiveType(string archivePath)
        {
            var extension = Path.GetExtension(archivePath).ToLowerInvariant();
            switch (extension)
            {
                case ".zip":
                    return ArchiveType.Zip;
                case ".7z":
                    return ArchiveType.SevenZip;
                case ".tar":
                    return ArchiveType.Tar;
                case ".gz":
                    return ArchiveType.GZip;
                default:
                    throw new NotSupportedException("Archive type not supported for writing.");
            }
        }

        private WriterOptions GetWriterOptions(ArchiveType archiveType)
        {
            switch (archiveType)
            {
                case ArchiveType.Tar:
                    return new WriterOptions(CompressionType.None); // TAR doesn't support compression
                case ArchiveType.Zip:
                case ArchiveType.SevenZip:
                case ArchiveType.GZip:
                default:
                    return new WriterOptions(CompressionType.Deflate);
            }
        }
    }
} 