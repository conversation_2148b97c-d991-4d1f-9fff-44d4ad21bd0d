using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using SharpCompress.Archives;
using SharpCompress.Common;
using SharpCompress.Writers;

namespace NestedArchiveNavigator.Avalonia.Core
{
    public class ArchiveNode
    {
        public string Path { get; set; } = string.Empty;
        public string? ExtractedDir { get; set; }
        public ArchiveNode? Parent { get; set; }
        public List<ArchiveNode> Children { get; set; } = new();
        public bool IsModified { get; set; }

        public ArchiveNode(string path, ArchiveNode? parent = null)
        {
            Path = path;
            Parent = parent;
        }

        public async Task RepackAsync()
        {
            // First, repack all children recursively
            foreach (var child in Children)
            {
                await child.RepackAsync();
            }

            // Then repack this archive if it has an extracted directory
            if (!string.IsNullOrEmpty(ExtractedDir) && Directory.Exists(ExtractedDir))
            {
                Console.WriteLine($"DEBUG: Repacking {Path}");
                
                if (Path.EndsWith(".tgz", StringComparison.OrdinalIgnoreCase))
                {
                    await RepackTgzAsync();
                }
                else if (Path.EndsWith(".tar", StringComparison.OrdinalIgnoreCase))
                {
                    await RepackTarAsync();
                }
                else if (Path.EndsWith(".zip", StringComparison.OrdinalIgnoreCase))
                {
                    await RepackZipAsync();
                }
                
                Console.WriteLine($"DEBUG: Repacked {Path} successfully");
            }
        }

        private async Task RepackTgzAsync()
        {
            using var fileStream = File.Create(Path);
            using var writer = WriterFactory.Open(fileStream, ArchiveType.Tar, new WriterOptions(CompressionType.GZip));
            
            await AddDirectoryToArchive(writer, ExtractedDir!);
        }

        private async Task RepackTarAsync()
        {
            // Ensure any existing file handles are closed
            GC.Collect();
            GC.WaitForPendingFinalizers();
            
            // Delete the existing file if it exists
            if (File.Exists(Path))
            {
                File.Delete(Path);
            }
            
            using var fileStream = File.Create(Path);
            using var writer = WriterFactory.Open(fileStream, ArchiveType.Tar, new WriterOptions(CompressionType.None));
            
            await AddDirectoryToArchive(writer, ExtractedDir!);
        }

        private async Task RepackZipAsync()
        {
            using var fileStream = File.Create(Path);
            using var writer = WriterFactory.Open(fileStream, ArchiveType.Zip, new WriterOptions(CompressionType.Deflate));
            
            await AddDirectoryToArchive(writer, ExtractedDir!);
        }

        private async Task AddDirectoryToArchive(IWriter writer, string directoryPath)
        {
            var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
            
            foreach (var file in files)
            {
                var relativePath = System.IO.Path.GetRelativePath(directoryPath, file);
                // Normalize path separators for archives
                relativePath = relativePath.Replace('\\', '/');
                
                using var fileStream = File.OpenRead(file);
                writer.Write(relativePath, fileStream);
            }
        }

        public void MarkAsModified()
        {
            IsModified = true;
            // Mark all parents as modified too
            var current = Parent;
            while (current != null)
            {
                current.IsModified = true;
                current = current.Parent;
            }
        }
    }
}