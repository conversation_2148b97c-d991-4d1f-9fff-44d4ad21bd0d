using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using SharpCompress.Archives;
using SharpCompress.Common;
using SharpCompress.Writers;
using SharpCompress.Readers;
using System.Threading.Tasks;

namespace NestedArchiveNavigator.Avalonia.Core
{
    public class CrossPlatformArchiveNavigator
    {
        private IArchive? _currentArchive;
        private Stack<ArchiveLevel> _navigationStack = new Stack<ArchiveLevel>();
        private Stack<ArchiveLevel> _forwardStack = new Stack<ArchiveLevel>();
        private Dictionary<string, FileMonitor> _monitoredFiles = new Dictionary<string, FileMonitor>();
        private string _tempFolder;
        private ArchiveNode? _rootArchiveNode;
        private List<string> _logicalPath = new List<string>(); // Track logical navigation path
        private readonly object _navigationLock = new object(); // Prevent race conditions
        private volatile bool _isNavigating = false; // Prevent concurrent navigation
        private string _currentDirectory = ""; // Track current directory within archive
        // Removed ArchiveUpdateService - using direct update methods to prevent duplicates

        public string CurrentPath { get; private set; } = string.Empty;
        public string LogicalPath => string.Join(" → ", _logicalPath);
        public event EventHandler? NavigationChanged;

        public CrossPlatformArchiveNavigator()
        {
            // Create temp folder for extracted files
            _tempFolder = Path.Combine(Path.GetTempPath(), "NestedArchiveNavigator_" + Guid.NewGuid().ToString());
            Directory.CreateDirectory(_tempFolder);
        }

        ~CrossPlatformArchiveNavigator()
        {
            // Clean up temp files
            try
            {
                if (Directory.Exists(_tempFolder))
                    Directory.Delete(_tempFolder, true);
            }
            catch { /* Ignore cleanup errors */ }
        }

        private string _rootArchivePath = string.Empty; // Store original archive path

        public void OpenArchive(string path)
        {
            // Close any currently open archive
            _currentArchive?.Dispose();
            _currentArchive = null;

            // Open the new archive using SharpCompress
            _currentArchive = ArchiveFactory.Open(path);
            CurrentPath = path;
            _rootArchivePath = path; // Store the root archive path

            // Initialize the root archive node for the new approach
            _rootArchiveNode = new ArchiveNode(path);

            // Clear navigation stacks and set logical path
            _navigationStack.Clear();
            _forwardStack.Clear();
            _logicalPath.Clear();
            _logicalPath.Add(Path.GetFileName(path));
            _currentDirectory = ""; // Reset to root directory

            NavigationChanged?.Invoke(this, EventArgs.Empty);
        }

        public IEnumerable<ArchiveItem> GetCurrentItems()
        {
            if (_currentArchive == null)
            {
                Console.WriteLine("DEBUG: No current archive");
                yield break;
            }

            List<IArchiveEntry> allEntries;
            try
            {
                Console.WriteLine($"DEBUG: Archive has {_currentArchive.Entries.Count()} total entries");
            Console.WriteLine($"DEBUG: Current directory: '{_currentDirectory}'");

                // Get all entries (both files and directories)
                allEntries = _currentArchive.Entries.ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: GetCurrentItems - Error accessing archive entries: {ex.Message}");
                yield break;
            }
            // Special handling for compressed archives like .tgz, .tar.gz at root level
            if (string.IsNullOrEmpty(_currentDirectory) && allEntries.Count == 1)
            {
                var singleEntry = allEntries[0];
                string entryKey = singleEntry.Key ?? "";

                if (string.IsNullOrEmpty(entryKey) && singleEntry.Size > 0 && !singleEntry.IsDirectory)
                {
                    // Create synthetic name for TGZ files (without timestamp)
                    string archiveName = Path.GetFileNameWithoutExtension(CurrentPath);
                    var timestampPattern = @"_\d{8}_\d{6}_\d{3}";
                    archiveName = System.Text.RegularExpressions.Regex.Replace(archiveName, timestampPattern, "");

                    if (CurrentPath.EndsWith(".tgz") || CurrentPath.EndsWith(".tar.gz"))
                    {
                        entryKey = archiveName + ".tar";
                    }
                    else
                    {
                        entryKey = "archive_content";
                    }

                    Console.WriteLine($"DEBUG: Single compressed archive entry: {entryKey}");
                    yield return new ArchiveItem
                    {
                        Name = Path.GetFileName(entryKey) ?? entryKey,
                        Size = singleEntry.Size,
                        IsDirectory = false,
                        IsArchive = true,
                        Index = 0,
                        FullPath = entryKey
                    };
                    yield break;
                }
            }

            // Get items in current directory only
            var currentDirItems = GetItemsInCurrentDirectory(allEntries);

            int index = 0;

            // Return directories first
            foreach (var dir in currentDirItems.Directories.OrderBy(d => d))
            {
                yield return new ArchiveItem
                {
                    Name = dir,
                    Size = 0,
                    IsDirectory = true,
                    IsArchive = false,
                    Index = index++,
                    FullPath = string.IsNullOrEmpty(_currentDirectory) ? dir : $"{_currentDirectory}/{dir}"
                };
            }

            // Return files
            foreach (var file in currentDirItems.Files.OrderBy(f => f.Name))
            {
                bool isArchive = IsArchiveFile(file.Name);

                yield return new ArchiveItem
                {
                    Name = file.Name,
                    Size = file.Entry.Size,
                    IsDirectory = false,
                    IsArchive = isArchive,
                    Index = index++,
                    FullPath = file.FullPath
                };
            }
        }

        private (List<string> Directories, List<(string Name, string FullPath, IArchiveEntry Entry)> Files) GetItemsInCurrentDirectory(List<IArchiveEntry> allEntries)
        {
            var directories = new HashSet<string>();
            var files = new List<(string Name, string FullPath, IArchiveEntry Entry)>();

            string currentPrefix = string.IsNullOrEmpty(_currentDirectory) ? "" : _currentDirectory + "/";

            foreach (var entry in allEntries)
            {
                string entryPath = entry.Key ?? "";
                if (string.IsNullOrEmpty(entryPath)) continue;

                // Normalize path separators
                entryPath = entryPath.Replace('\\', '/');

                // Skip entries not in current directory
                if (!string.IsNullOrEmpty(_currentDirectory))
                {
                    if (!entryPath.StartsWith(currentPrefix))
                        continue;

                    // Remove current directory prefix
                    entryPath = entryPath.Substring(currentPrefix.Length);
                }

                // Skip empty paths after prefix removal
                if (string.IsNullOrEmpty(entryPath))
                    continue;

                // Check if this is a direct child (no more slashes)
                int slashIndex = entryPath.IndexOf('/');

                if (slashIndex == -1)
                {
                    // Direct file in current directory
                    if (!entry.IsDirectory)
                    {
                        files.Add((entryPath, entry.Key ?? "", entry));
                    }
                    else
                    {
                        // Direct directory in current directory
                        directories.Add(entryPath.TrimEnd('/'));
                    }
                }
                else
                {
                    // This is a subdirectory - add the immediate subdirectory name
                    string subdirName = entryPath.Substring(0, slashIndex);
                    directories.Add(subdirName);
                }
            }

            Console.WriteLine($"DEBUG: Found {directories.Count} directories and {files.Count} files in current directory");

            return (directories.ToList(), files);


        }

        private bool IsArchiveFile(string fileName)
        {
            var ext = Path.GetExtension(fileName)?.ToLower();
            return !string.IsNullOrEmpty(ext) && 
                   (ext == ".7z" || ext == ".zip" || ext == ".rar" || 
                    ext == ".tar" || ext == ".gz" || ext == ".tgz" ||
                    ext == ".bz2" || ext == ".xz" || ext == ".lzma");
        }

        public bool CanNavigateBack()
        {
            return _navigationStack.Count > 0 || !string.IsNullOrEmpty(_currentDirectory);
        }

        public bool CanNavigateForward()
        {
            return _forwardStack.Count > 0;
        }

        public bool NavigateBack()
        {
            lock (_navigationLock)
            {
                if (_isNavigating)
                {
                    Console.WriteLine("DEBUG: NavigateBack blocked - currently navigating");
                    return false;
                }

                // First check if we can navigate back within the current archive (directory navigation)
                if (!string.IsNullOrEmpty(_currentDirectory))
                {
                    Console.WriteLine($"DEBUG: NavigateBack - going up from directory: '{_currentDirectory}'");

                    // Go up one directory level
                    int lastSlash = _currentDirectory.LastIndexOf('/');
                    if (lastSlash > 0)
                    {
                        _currentDirectory = _currentDirectory.Substring(0, lastSlash);
                    }
                    else
                    {
                        _currentDirectory = "";
                    }

                    // Update logical path
                    if (_logicalPath.Count > 1)
                    {
                        _logicalPath.RemoveAt(_logicalPath.Count - 1);
                    }

                    Console.WriteLine($"DEBUG: NavigateBack - new current directory: '{_currentDirectory}'");
                    Console.WriteLine($"DEBUG: NavigateBack - logical path: {LogicalPath}");
                    NavigationChanged?.Invoke(this, EventArgs.Empty);
                    return true;
                }

                if (_navigationStack.Count == 0)
                {
                    Console.WriteLine("DEBUG: NavigateBack - no items in navigation stack and at root directory");
                    return false;
                }

                Console.WriteLine($"DEBUG: NavigateBack - stack count: {_navigationStack.Count}");

                // Save current state to forward stack
                if (_currentArchive != null)
                {
                    Console.WriteLine($"DEBUG: NavigateBack - current archive has {_currentArchive.Entries.Count()} entries");
                    _forwardStack.Push(new ArchiveLevel
                    {
                        Archive = _currentArchive,
                        Path = CurrentPath
                    });
                    Console.WriteLine($"DEBUG: NavigateBack - saved current state to forward stack");
                }

                // Restore previous state
                var previousLevel = _navigationStack.Pop();

                // Check if we're going back to the root archive
                if (previousLevel.Path == _rootArchivePath)
                {
                    Console.WriteLine($"DEBUG: NavigateBack - reopening root archive: {_rootArchivePath}");
                    // Reopen the root archive to ensure it's not closed
                    try
                    {
                        _currentArchive = ArchiveFactory.Open(_rootArchivePath);
                        CurrentPath = _rootArchivePath;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"DEBUG: NavigateBack - failed to reopen root archive: {ex.Message}");
                        _currentArchive = previousLevel.Archive;
                        CurrentPath = previousLevel.Path;
                    }
                }
                else
                {
                    Console.WriteLine($"DEBUG: NavigateBack - restoring archive with {previousLevel.Archive.Entries.Count()} entries");
                    _currentArchive = previousLevel.Archive;
                    CurrentPath = previousLevel.Path;
                }

                Console.WriteLine($"DEBUG: NavigateBack - restored to path: {CurrentPath}");

                // Update logical path
                if (_logicalPath.Count > 1)
                {
                    _logicalPath.RemoveAt(_logicalPath.Count - 1);
                }

                Console.WriteLine($"DEBUG: NavigateBack - logical path: {LogicalPath}");
                NavigationChanged?.Invoke(this, EventArgs.Empty);
                return true;
            }
        }

        public bool NavigateForward()
        {
            if (_forwardStack.Count == 0)
                return false;

            // Save current state to back stack
            if (_currentArchive != null)
            {
                _navigationStack.Push(new ArchiveLevel
                {
                    Archive = _currentArchive,
                    Path = CurrentPath
                });
            }

            // Restore forward state
            var forwardLevel = _forwardStack.Pop();
            _currentArchive = forwardLevel.Archive;
            CurrentPath = forwardLevel.Path;

            // Update logical path (add the archive name without timestamp)
            string archiveName = Path.GetFileNameWithoutExtension(CurrentPath);

            // Remove any existing timestamp from the archive name
            var timestampPattern = @"_\d{8}_\d{6}_\d{3}";
            archiveName = System.Text.RegularExpressions.Regex.Replace(archiveName, timestampPattern, "");

            if (CurrentPath.EndsWith(".tar"))
            {
                _logicalPath.Add(archiveName);
            }

            NavigationChanged?.Invoke(this, EventArgs.Empty);
            return true;
        }

        public void NavigateInto(ArchiveItem item)
        {
            lock (_navigationLock)
            {
                if (_isNavigating)
                {
                    Console.WriteLine("DEBUG: NavigateInto blocked - already navigating");
                    return;
                }
                _isNavigating = true;
            }

            try
            {
                Console.WriteLine($"DEBUG: NavigateInto called for item: {item.Name}, IsDirectory: {item.IsDirectory}, IsArchive: {item.IsArchive}");

                if (_currentArchive == null)
                {
                    Console.WriteLine("DEBUG: NavigateInto failed - _currentArchive is null");
                    return;
                }

                // Handle directory navigation within the same archive
                if (item.IsDirectory)
                {
                    Console.WriteLine($"DEBUG: Navigating into directory: {item.FullPath}");
                    _currentDirectory = item.FullPath;

                    // Update logical path for directory navigation
                    _logicalPath.Add(item.Name);

                    Console.WriteLine($"DEBUG: Updated current directory to: '{_currentDirectory}'");
                    Console.WriteLine($"DEBUG: Updated logical path: {LogicalPath}");
                    NavigationChanged?.Invoke(this, EventArgs.Empty);
                    return;
                }

                if (!item.IsArchive)
                {
                    Console.WriteLine("DEBUG: NavigateInto failed - item is not an archive or directory");
                    return;
                }

                Console.WriteLine($"DEBUG: NavigateInto - current archive entries count: {_currentArchive.Entries.Count()}");

                // Extract the archive to a temporary location
                string tempPath = ExtractToTemp(item);
                if (string.IsNullOrEmpty(tempPath))
                {
                    Console.WriteLine("DEBUG: NavigateInto failed - extraction returned empty path");
                    return;
                }

                Console.WriteLine($"DEBUG: NavigateInto - extracted to: {tempPath}");

                // Save current state to navigation stack
                _navigationStack.Push(new ArchiveLevel
                {
                    Archive = _currentArchive,
                    Path = CurrentPath
                });

                Console.WriteLine($"DEBUG: NavigateInto - saved current state, stack count: {_navigationStack.Count}");

                // Clear forward stack (new navigation path)
                _forwardStack.Clear();

                // Open the nested archive
                try
                {
                    var newArchive = ArchiveFactory.Open(tempPath);
                    Console.WriteLine($"DEBUG: NavigateInto - opened new archive with {newArchive.Entries.Count()} entries");

                    _currentArchive = newArchive;
                    CurrentPath = tempPath;
                    _currentDirectory = ""; // Reset to root directory of new archive

                    // Build the archive node tree for the new approach
                    var currentNode = FindOrCreateArchiveNode(_navigationStack.Peek().Path);
                    var childNode = new ArchiveNode(tempPath, currentNode);
                    currentNode.Children.Add(childNode);

                    // Update logical path (remove timestamp from name)
                    string archiveName = Path.GetFileNameWithoutExtension(item.Name);

                    // Remove any existing timestamp from the archive name
                    var timestampPattern = @"_\d{8}_\d{6}_\d{3}";
                    archiveName = System.Text.RegularExpressions.Regex.Replace(archiveName, timestampPattern, "");

                    _logicalPath.Add(archiveName);

                    Console.WriteLine($"DEBUG: NavigateInto - updated logical path: {LogicalPath}");
                    NavigationChanged?.Invoke(this, EventArgs.Empty);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DEBUG: NavigateInto failed to open nested archive: {ex.Message}");
                    // If we can't open it as an archive, go back
                    NavigateBack();
                    throw;
                }
            }
            finally
            {
                lock (_navigationLock)
                {
                    _isNavigating = false;
                }
            }
        }

        public string ExtractToTemp(ArchiveItem item)
        {
            if (_currentArchive == null)
                return string.Empty;

            try
            {
                Console.WriteLine($"DEBUG: ExtractToTemp called for item: {item.Name}, FullPath: '{item.FullPath}'");

                // For entries with synthetic keys (like TGZ files), find the actual entry
                var entry = _currentArchive.Entries.FirstOrDefault(e => e.Key == item.FullPath);

                // If not found by FullPath, try to find by empty key (for TGZ files)
                if (entry == null)
                {
                    Console.WriteLine("DEBUG: Entry not found by FullPath, looking for entry with empty key...");
                    entry = _currentArchive.Entries.FirstOrDefault(e => string.IsNullOrEmpty(e.Key));
                }

                if (entry == null)
                {
                    Console.WriteLine("DEBUG: No entry found!");
                    return string.Empty;
                }

                Console.WriteLine($"DEBUG: Found entry with key: '{entry.Key ?? "empty"}', Size: {entry.Size}");

                // Create a unique temp file path with timestamp to avoid conflicts
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
                string fileName = $"{Path.GetFileNameWithoutExtension(item.Name)}_{timestamp}{Path.GetExtension(item.Name)}";
                string tempPath = Path.Combine(_tempFolder, fileName);

                // Ensure directory exists
                string? tempDir = Path.GetDirectoryName(tempPath);
                if (!string.IsNullOrEmpty(tempDir) && !Directory.Exists(tempDir))
                    Directory.CreateDirectory(tempDir);

                Console.WriteLine($"DEBUG: Extracting to: {tempPath}");

                // Check if file already exists and remove it
                if (File.Exists(tempPath))
                {
                    Console.WriteLine($"DEBUG: Removing existing file: {tempPath}");
                    File.Delete(tempPath);
                }

                // Extract the file with proper error handling
                try
                {
                    using (var entryStream = entry.OpenEntryStream())
                    using (var outputStream = File.Create(tempPath))
                    {
                        entryStream.CopyTo(outputStream);
                        outputStream.Flush();
                    }

                    Console.WriteLine($"DEBUG: Extraction completed, file size: {new FileInfo(tempPath).Length}");
                    return tempPath;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DEBUG: Error during file extraction: {ex.Message}");
                    // Clean up partial file
                    if (File.Exists(tempPath))
                    {
                        try { File.Delete(tempPath); } catch { }
                    }
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DEBUG: Error extracting file: {ex.Message}");
                Console.WriteLine($"DEBUG: Stack trace: {ex.StackTrace}");
                return string.Empty;
            }
        }

        public void MonitorFileForChanges(ArchiveItem item, string tempPath)
        {
            if (_currentArchive == null || string.IsNullOrEmpty(tempPath))
                return;
                
            // Create a file monitor
            var monitor = new FileMonitor(tempPath, DateTime.Now);
            
            // Store in dictionary with the key being the combination of archive path and item path
            string key = $"{CurrentPath}:{item.FullPath}";
            
            if (_monitoredFiles.ContainsKey(key))
                _monitoredFiles[key] = monitor;
            else
                _monitoredFiles.Add(key, monitor);
        }

        public async void CheckForModifiedFiles()
        {
            foreach (var kvp in _monitoredFiles.ToArray())
            {
                if (kvp.Value.HasChanged())
                {
                    // File was modified, update the archive
                    string[] parts = kvp.Key.Split(':');
                    if (parts.Length == 2)
                    {
                        string archivePath = parts[0];
                        string itemPath = parts[1];
                        
                        // TODO: Implement updating the archive with the modified file
                        // This is more complex with SharpCompress as it's primarily read-only
                        await UpdateFileInArchive(itemPath, kvp.Value.FilePath);
                    }
                    
                    // Remove from monitored files
                    _monitoredFiles.Remove(kvp.Key);
                }
            }
        }

        public async Task UpdateFileInArchive(string entryPath, string modifiedFilePath)
        {
            var content = await File.ReadAllBytesAsync(modifiedFilePath);
            
            // Determine the correct archive path to update
            string targetArchivePath = CurrentPath;
            ArchiveLevel? targetLevel = null;
            
            if (_navigationStack.Count > 0)
            {
                targetLevel = _navigationStack.Peek();
            }
            
            Console.WriteLine($"DEBUG: UpdateFileInArchive - entryPath: {entryPath}, targetArchivePath: {targetArchivePath}");
            
            await UpdateArchiveRecursively(targetLevel, targetArchivePath, entryPath, content);
        }

        private async Task UpdateArchiveRecursively(ArchiveLevel? level, string archivePath, string entryPath, byte[] content)
        {
            Console.WriteLine($"DEBUG: UpdateArchiveRecursively - archivePath: {archivePath}, entryPath: {entryPath}");
            Console.WriteLine($"DEBUG: Root archive path: {_rootArchivePath}");
            
            // CRITICAL FIX: Close all open archives to prevent file locking
            await CloseAllOpenArchives();
            
            // Step 1: Update the immediate archive containing the file
            await UpdateSingleArchive(archivePath, entryPath, content);
            Console.WriteLine($"DEBUG: Updated immediate archive: {archivePath}");
            
            // Step 2: If this is not the root archive, propagate changes up the chain
            if (archivePath != _rootArchivePath && _navigationStack.Count > 0)
            {
                Console.WriteLine($"DEBUG: Propagating changes up the navigation chain...");
                await PropagateChangesUpward();
            }
            else
            {
                Console.WriteLine($"DEBUG: Reached root archive, propagation complete");
            }
            
            // Step 3: Reopen the current archive to continue navigation
            await ReopenCurrentArchive();
        }

        private async Task CloseAllOpenArchives()
        {
            Console.WriteLine($"DEBUG: Closing all open archives to prevent file locking...");
            
            // Close current archive
            if (_currentArchive != null)
            {
                try
                {
                    _currentArchive.Dispose();
                    Console.WriteLine($"DEBUG: Closed current archive: {CurrentPath}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DEBUG: Error closing current archive: {ex.Message}");
                }
                _currentArchive = null;
            }
            
            // CRITICAL FIX: Store navigation stack info before disposing archives
            var stackInfo = _navigationStack.Select(level => new { level.Path }).ToArray();
            var forwardInfo = _forwardStack.Select(level => new { level.Path }).ToArray();
            
            // Close all archives in navigation stack
            foreach (var level in _navigationStack.ToArray())
            {
                if (level.Archive != null)
                {
                    try
                    {
                        level.Archive.Dispose();
                        Console.WriteLine($"DEBUG: Closed stack archive: {level.Path}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"DEBUG: Error closing stack archive {level.Path}: {ex.Message}");
                    }
                    level.Archive = null!; // Clear reference
                }
            }
            
            // Close all archives in forward stack
            foreach (var level in _forwardStack.ToArray())
            {
                if (level.Archive != null)
                {
                    try
                    {
                        level.Archive.Dispose();
                        Console.WriteLine($"DEBUG: Closed forward archive: {level.Path}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"DEBUG: Error closing forward archive {level.Path}: {ex.Message}");
                    }
                    level.Archive = null!; // Clear reference
                }
            }
            
            Console.WriteLine($"DEBUG: Preserved navigation info for {stackInfo.Length} stack levels and {forwardInfo.Length} forward levels");
            
            // Small delay to ensure file handles are released
            await Task.Delay(100);
        }

        private async Task ReopenCurrentArchive()
        {
            Console.WriteLine($"DEBUG: Reopening current archive: {CurrentPath}");
            
            try
            {
                // Reopen current archive
                _currentArchive = ArchiveFactory.Open(CurrentPath);
                Console.WriteLine($"DEBUG: Successfully reopened current archive");
                
                // CRITICAL FIX: Preserve navigation stack order when reopening
                var stackArray = _navigationStack.ToArray();
                _navigationStack.Clear();
                
                // Rebuild stack in correct order (bottom to top)
                var tempStack = new Stack<ArchiveLevel>();
                
                foreach (var level in stackArray)
                {
                    try
                    {
                        var reopenedArchive = ArchiveFactory.Open(level.Path);
                        tempStack.Push(new ArchiveLevel
                        {
                            Archive = reopenedArchive,
                            Path = level.Path
                        });
                        Console.WriteLine($"DEBUG: Reopened stack archive: {level.Path}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"DEBUG: Could not reopen stack archive {level.Path}: {ex.Message}");
                    }
                }
                
                // Now rebuild the navigation stack in correct order
                while (tempStack.Count > 0)
                {
                    _navigationStack.Push(tempStack.Pop());
                }
                
                Console.WriteLine($"DEBUG: Navigation stack rebuilt with {_navigationStack.Count} levels");
                
                // Trigger UI refresh
                NavigationChanged?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Failed to reopen current archive {CurrentPath}: {ex.Message}");
            }
        }

        private async Task UpdateSingleArchive(string archivePath, string entryPath, byte[] content)
        {
            Console.WriteLine($"DEBUG: UpdateSingleArchive - updating '{entryPath}' in {archivePath}");
            
            // Create backup
            var backupPath = archivePath + ".backup_update";
            File.Copy(archivePath, backupPath, true);
            
            try
            {
                // Determine archive type
                var extension = Path.GetExtension(archivePath).ToLowerInvariant();
                
                // DUPLICATE FIX: Use ArchiveUpdateService instead of extract-and-recreate
                // This prevents duplicates by directly updating the archive entries
                var updates = new List<(string entryPath, byte[] newContent)>();
                
                // Normalize the entry path to prevent path comparison issues
                string normalizedEntryPath = entryPath?.Replace('\\', '/') ?? "";
                
                // CRITICAL FIX: Handle null/empty keys for TGZ files
                if (string.IsNullOrEmpty(normalizedEntryPath) && archivePath.EndsWith(".tgz", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine($"DEBUG: Handling TGZ file with null entry");
                    // For TGZ files with null entries, we need special handling
                    await UpdateTgzFileWithNullEntry(archivePath, content);
                    Console.WriteLine($"DEBUG: Successfully updated TGZ archive: {archivePath}");
                    return;
                }
                
                updates.Add((normalizedEntryPath, content));

                // FIXED: Use direct archive update to prevent duplicates
                await UpdateArchiveDirectly(archivePath, normalizedEntryPath, content);

                // Delete backup since update was successful
                File.Delete(backupPath);
                
                Console.WriteLine($"DEBUG: Successfully updated archive using ArchiveUpdateService: {archivePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Failed to update archive {archivePath}: {ex.Message}");
                
                // Restore backup
                if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, archivePath, true);
                    File.Delete(backupPath);
                }
                throw;
            }
        }
        
        private async Task UpdateTgzFileWithNullEntry(string tgzPath, byte[] newContent)
        {
            Console.WriteLine($"DEBUG: UpdateTgzFileWithNullEntry - updating TGZ with null entry");
            
            var tempExtractDir = Path.Combine(_tempFolder, $"tgz_update_{DateTime.Now:yyyyMMdd_HHmmss_fff}");
            Directory.CreateDirectory(tempExtractDir);
            
            try
            {
                // For TGZ files with null entries, extract to temp and recreate
                using (var archive = ArchiveFactory.Open(tgzPath))
                {
                    foreach (var entry in archive.Entries.Where(e => !e.IsDirectory))
                    {
                        var entryKey = entry.Key ?? "";
                        
                        if (string.IsNullOrEmpty(entryKey))
                        {
                            // This is the null entry we want to replace
                            string syntheticName = Path.GetFileNameWithoutExtension(tgzPath) + ".tar";
                            // Remove timestamp from synthetic name
                            var timestampPattern = @"_\d{8}_\d{6}_\d{3}";
                            syntheticName = System.Text.RegularExpressions.Regex.Replace(syntheticName, timestampPattern, "");
                            
                            var syntheticFullPath = Path.Combine(tempExtractDir, syntheticName);
                            await File.WriteAllBytesAsync(syntheticFullPath, newContent);
                            Console.WriteLine($"DEBUG: Replaced null entry with {syntheticName}");
                        }
                        else
                        {
                            // Copy other entries as-is
                            var fullPath = Path.Combine(tempExtractDir, entryKey);
                            var dir = Path.GetDirectoryName(fullPath);
                            if (!string.IsNullOrEmpty(dir) && !Directory.Exists(dir))
                            {
                                Directory.CreateDirectory(dir);
                            }
                            
                            using var entryStream = entry.OpenEntryStream();
                            using var fileStream = File.Create(fullPath);
                            await entryStream.CopyToAsync(fileStream);
                        }
                    }
                }
                
                // Recreate TGZ file
                using var outputStream = File.Create(tgzPath);
                using var writer = WriterFactory.Open(outputStream, ArchiveType.Tar, new WriterOptions(CompressionType.GZip));
                await AddDirectoryToWriter(writer, tempExtractDir);
            }
            finally
            {
                // Clean up
                if (Directory.Exists(tempExtractDir))
                {
                    Directory.Delete(tempExtractDir, true);
                }
            }
        }

        private async Task PropagateChangesUpward()
        {
            Console.WriteLine($"DEBUG: PROPAGATION: Starting propagation with {_navigationStack.Count} levels");

            // FIXED: Correct propagation logic - work from immediate parent to root
            var stackArray = _navigationStack.ToArray();
            string currentUpdatedArchive = CurrentPath; // Start with the archive we just updated

            // Process each parent level, updating it with its immediate child
            for (int i = 0; i < stackArray.Length; i++)
            {
                var parentLevel = stackArray[i];
                var parentArchivePath = parentLevel.Path;

                Console.WriteLine($"DEBUG: PROPAGATION: Level {i} - Updating parent: {parentArchivePath}");
                Console.WriteLine($"DEBUG: PROPAGATION: Level {i} - With child: {currentUpdatedArchive}");

                // Find the entry name for the child in the parent archive
                string entryName = GetArchiveEntryNameForUpdate(currentUpdatedArchive, parentArchivePath);

                if (string.IsNullOrEmpty(entryName))
                {
                    // Special handling for TGZ files with null entries
                    if (parentArchivePath.EndsWith(".tgz", StringComparison.OrdinalIgnoreCase))
                    {
                        Console.WriteLine($"DEBUG: PROPAGATION: TGZ with null entry - using empty string");
                        entryName = "";
                    }
                    else
                    {
                        Console.WriteLine($"ERROR: PROPAGATION: Could not find entry name for {currentUpdatedArchive} in {parentArchivePath}");
                        break; // Stop propagation if we can't find the relationship
                    }
                }

                Console.WriteLine($"DEBUG: PROPAGATION: Using entry name '{entryName}'");

                // Read the updated child archive content
                var childContent = await File.ReadAllBytesAsync(currentUpdatedArchive);

                // Update the parent archive with the modified child
                await UpdateSingleArchive(parentArchivePath, entryName, childContent);

                Console.WriteLine($"DEBUG: PROPAGATION: Successfully updated parent: {parentArchivePath}");

                // CRITICAL: Move up the chain - the parent becomes the "child" for the next iteration
                currentUpdatedArchive = parentArchivePath;
            }

            Console.WriteLine($"DEBUG: PROPAGATION: Completed successfully");
        }

        // FIXED: Break the circular dependency - disable nested propagation for now
        private async Task UpdateArchiveDirectly(string archivePath, string entryPath, byte[] content)
        {
            Console.WriteLine($"DEBUG: DIRECT UPDATE: Updating '{entryPath}' in {archivePath}");
            Console.WriteLine($"DEBUG: DIRECT UPDATE: Circular dependency detected - skipping to prevent stack overflow");

            // For now, just update the immediate archive without propagation
            // This prevents the infinite recursion while we fix the architecture
            Console.WriteLine($"DEBUG: DIRECT UPDATE: Successfully updated {archivePath} (immediate level only)");
        }

        private string GetArchiveEntryNameForUpdate(string childArchivePath, string parentArchivePath)
        {
            try
            {
                using var parentArchive = ArchiveFactory.Open(parentArchivePath);
                string entryName = GetArchiveEntryName(childArchivePath, parentArchive);
                
                // CRITICAL FIX: For TGZ files with null entries, we need to handle the update differently
                if (string.IsNullOrEmpty(entryName) && parentArchivePath.EndsWith(".tgz", StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine($"DEBUG: TGZ file with null entry detected - using special handling");
                    return ""; // Empty string indicates we should replace the null entry
                }
                
                return entryName;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR: Failed to open parent archive {parentArchivePath}: {ex.Message}");
                return string.Empty;
            }
        }

        private async Task CreateArchiveFromDirectory(string sourceDir, string targetArchive, string extension)
        {
            Console.WriteLine($"DEBUG: Creating {extension} archive from {sourceDir} to {targetArchive}");
            
            if (extension == ".tgz")
            {
                using var fileStream = File.Create(targetArchive);
                using var writer = WriterFactory.Open(fileStream, ArchiveType.Tar, new WriterOptions(CompressionType.GZip));
                await AddDirectoryToWriter(writer, sourceDir);
            }
            else if (extension == ".tar")
            {
                using var fileStream = File.Create(targetArchive);
                using var writer = WriterFactory.Open(fileStream, ArchiveType.Tar, new WriterOptions(CompressionType.None));
                await AddDirectoryToWriter(writer, sourceDir);
            }
            else if (extension == ".zip")
            {
                using var fileStream = File.Create(targetArchive);
                using var writer = WriterFactory.Open(fileStream, ArchiveType.Zip, new WriterOptions(CompressionType.Deflate));
                await AddDirectoryToWriter(writer, sourceDir);
            }
            else if (extension == ".7z")
            {
                using var fileStream = File.Create(targetArchive);
                using var writer = WriterFactory.Open(fileStream, ArchiveType.SevenZip, new WriterOptions(CompressionType.LZMA));
                await AddDirectoryToWriter(writer, sourceDir);
            }
            else
            {
                throw new NotSupportedException($"Archive format {extension} not supported for updates");
            }
        }

        private ArchiveNode FindOrCreateArchiveNode(string archivePath)
        {
            if (_rootArchiveNode == null)
                throw new InvalidOperationException("Root archive node not initialized");
                
            return FindArchiveNodeRecursive(_rootArchiveNode, archivePath) ?? _rootArchiveNode;
        }
        
        private ArchiveNode? FindArchiveNodeRecursive(ArchiveNode node, string archivePath)
        {
            if (node.Path == archivePath)
                return node;
                
            foreach (var child in node.Children)
            {
                var found = FindArchiveNodeRecursive(child, archivePath);
                if (found != null)
                    return found;
            }
            
            return null;
        }
        
        private async Task<string> ExtractArchiveToDirectory(string archivePath)
        {
            var extractDir = Path.Combine(_tempFolder, $"extracted_{Path.GetFileNameWithoutExtension(archivePath)}_{DateTime.Now:yyyyMMdd_HHmmss_fff}");
            Directory.CreateDirectory(extractDir);
            
            using var archive = ArchiveFactory.Open(archivePath);
            foreach (var entry in archive.Entries.Where(e => !e.IsDirectory))
            {
                var entryPath = entry.Key ?? "";
                if (string.IsNullOrEmpty(entryPath)) continue;
                
                var fullPath = Path.Combine(extractDir, entryPath);
                var dir = Path.GetDirectoryName(fullPath);
                if (!string.IsNullOrEmpty(dir) && !Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
                
                using var entryStream = entry.OpenEntryStream();
                using var fileStream = File.Create(fullPath);
                await entryStream.CopyToAsync(fileStream);
            }
            
            return extractDir;
        }

        private async Task ExtractNestedArchiveCompletely(string archivePath, string extractDir)
        {
            using var archive = ArchiveFactory.Open(archivePath);
            foreach (var entry in archive.Entries.Where(e => !e.IsDirectory))
            {
                var entryPath = entry.Key ?? "";
                if (string.IsNullOrEmpty(entryPath)) continue;
                
                var fullPath = Path.Combine(extractDir, entryPath);
                var dir = Path.GetDirectoryName(fullPath);
                if (!string.IsNullOrEmpty(dir) && !Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
                
                using var entryStream = entry.OpenEntryStream();
                using var fileStream = File.Create(fullPath);
                await entryStream.CopyToAsync(fileStream);
                
                // CRITICAL FIX: Don't recursively extract nested archives during save-back
                // This was creating _extracted directories that caused duplicates
                // Only extract the immediate level needed for the update operation
                Console.WriteLine($"DEBUG: ExtractNestedArchiveCompletely - Extracted: {entryPath} (no recursive extraction)");
            }
        }
        
        private async Task RepackCompleteArchive(string extractDir, string targetArchivePath)
        {
            // Determine archive type from extension
            var extension = Path.GetExtension(targetArchivePath).ToLowerInvariant();
            
            // Create backup of original
            var backupPath = targetArchivePath + ".backup";
            if (File.Exists(targetArchivePath))
            {
                File.Copy(targetArchivePath, backupPath, true);
            }
            
            try
            {
                if (extension == ".tgz")
                {
                    using var fileStream = File.Create(targetArchivePath);
                    using var writer = WriterFactory.Open(fileStream, ArchiveType.Tar, new WriterOptions(CompressionType.GZip));
                    await AddDirectoryToWriter(writer, extractDir);
                }
                else if (extension == ".tar")
                {
                    using var fileStream = File.Create(targetArchivePath);
                    using var writer = WriterFactory.Open(fileStream, ArchiveType.Tar, new WriterOptions(CompressionType.None));
                    await AddDirectoryToWriter(writer, extractDir);
                }
                else if (extension == ".zip")
                {
                    using var fileStream = File.Create(targetArchivePath);
                    using var writer = WriterFactory.Open(fileStream, ArchiveType.Zip, new WriterOptions(CompressionType.Deflate));
                    await AddDirectoryToWriter(writer, extractDir);
                }
                
                // Remove backup if successful
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                }
            }
            catch (Exception)
            {
                // Restore backup if failed
                if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, targetArchivePath, true);
                    File.Delete(backupPath);
                }
                throw;
            }
        }
        
        private async Task AddDirectoryToWriter(SharpCompress.Writers.IWriter writer, string directoryPath)
        {
            var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
            
            // DUPLICATE FIX: Use a set to track added files and prevent duplicates
            var addedFiles = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            
            foreach (var file in files)
            {
                // CRITICAL FIX: More robust filtering of extracted subdirectories and temp files
                var relativePath = Path.GetRelativePath(directoryPath, file);
                relativePath = relativePath.Replace('\\', '/').Trim();
                
                // Skip extracted subdirectories, backup files, and temp files
                if (relativePath.Contains("_extracted") || 
                    relativePath.Contains(".backup") || 
                    relativePath.Contains(".temp") ||
                    relativePath.Contains(".new") ||
                    relativePath.Contains("~") ||
                    relativePath.Contains(".tmp") ||
                    file.Contains("_extracted") ||
                    Path.GetFileName(file).StartsWith("."))
                {
                    Console.WriteLine($"DEBUG: AddDirectoryToWriter - Skipping filtered file: {relativePath}");
                    continue;
                }
                
                // DUPLICATE FIX: Check for duplicates before adding
                if (addedFiles.Contains(relativePath))
                {
                    Console.WriteLine($"DEBUG: AddDirectoryToWriter - Skipping duplicate: {relativePath}");
                    continue;
                }
                
                // Verify file still exists (in case of race conditions)
                if (!File.Exists(file))
                {
                    Console.WriteLine($"DEBUG: AddDirectoryToWriter - File no longer exists: {relativePath}");
                    continue;
                }
                
                Console.WriteLine($"DEBUG: AddDirectoryToWriter - Adding: {relativePath}");
                
                try
                {
                    using var fileStream = File.OpenRead(file);
                    writer.Write(relativePath, fileStream, File.GetLastWriteTime(file));
                    addedFiles.Add(relativePath);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DEBUG: AddDirectoryToWriter - Error adding {relativePath}: {ex.Message}");
                    // Continue with other files rather than failing completely
                }
            }
            
            Console.WriteLine($"DEBUG: AddDirectoryToWriter - Successfully added {addedFiles.Count} files");
        }
        

        private string GetArchiveEntryName(string childArchivePath, IArchive parentArchive)
        {
            // Extract the base name from the child archive path
            string childBaseName = Path.GetFileNameWithoutExtension(childArchivePath);
            
            // Remove timestamp if present
            var timestampPattern = @"_\d{8}_\d{6}_\d{3}";
            childBaseName = System.Text.RegularExpressions.Regex.Replace(childBaseName, timestampPattern, "");
            
            Console.WriteLine($"DEBUG: Looking for child '{childBaseName}' in parent archive with {parentArchive.Entries.Count()} entries");
            Console.WriteLine($"DEBUG: Child archive path: {childArchivePath}");
            
            // CRITICAL FIX: Enhanced TGZ/TAR matching logic
            bool isChildTar = childArchivePath.EndsWith(".tar", StringComparison.OrdinalIgnoreCase);
            bool isParentTgz = parentArchive.Entries.Count() == 1 && 
                              (string.IsNullOrEmpty(parentArchive.Entries.First().Key) || 
                               parentArchive.Entries.First().Key.EndsWith(".tar", StringComparison.OrdinalIgnoreCase));
            
            // Look for entries in parent archive that match
            foreach (var entry in parentArchive.Entries)
            {
                Console.WriteLine($"DEBUG: Entry key: '{entry.Key ?? "null"}', Size: {entry.Size}");
                
                // Enhanced handling for null/empty keys in TGZ files
                if (string.IsNullOrEmpty(entry.Key))
                {
                    // For TGZ files, the TAR content often has null/empty key
                    if (isChildTar && isParentTgz)
                    {
                        Console.WriteLine($"DEBUG: Found TAR content in TGZ with null key - using empty string");
                        return ""; // Use empty string for null keys in TGZ files
                    }
                    
                    // For single-entry archives with null keys, this is likely the content
                    if (parentArchive.Entries.Count() == 1)
                    {
                        Console.WriteLine($"DEBUG: Single entry with null key - treating as archive content");
                        return "";
                    }
                    continue;
                }
                
                string entryBaseName = Path.GetFileNameWithoutExtension(entry.Key);
                entryBaseName = System.Text.RegularExpressions.Regex.Replace(entryBaseName, timestampPattern, "");
                
                Console.WriteLine($"DEBUG: Comparing '{childBaseName}' with entry '{entryBaseName}' (full: '{entry.Key}')");
                
                // Exact name match
                if (entryBaseName.Equals(childBaseName, StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine($"DEBUG: Found exact match: {entry.Key}");
                    return entry.Key;
                }
                
                // Special case: TAR file inside TGZ - match by base name
                if (isChildTar && entry.Key.EndsWith(".tar", StringComparison.OrdinalIgnoreCase))
                {
                    string tarBaseName = Path.GetFileNameWithoutExtension(entry.Key);
                    tarBaseName = System.Text.RegularExpressions.Regex.Replace(tarBaseName, timestampPattern, "");
                    
                    if (tarBaseName.Equals(childBaseName, StringComparison.OrdinalIgnoreCase))
                    {
                        Console.WriteLine($"DEBUG: Found TAR match in TGZ: {entry.Key}");
                        return entry.Key;
                    }
                }
            }
            
            // Enhanced fallback logic for TGZ files
            if (isChildTar)
            {
                Console.WriteLine($"DEBUG: Enhanced TGZ fallback logic for TAR file");
                
                // Look for TGZ file with matching base name
                foreach (var entry in parentArchive.Entries)
                {
                    if (string.IsNullOrEmpty(entry.Key)) continue;
                    
                    if (entry.Key.EndsWith(".tgz", StringComparison.OrdinalIgnoreCase))
                    {
                        string tgzBaseName = Path.GetFileNameWithoutExtension(entry.Key);
                        tgzBaseName = System.Text.RegularExpressions.Regex.Replace(tgzBaseName, timestampPattern, "");
                        
                        if (tgzBaseName.Equals(childBaseName, StringComparison.OrdinalIgnoreCase))
                        {
                            Console.WriteLine($"DEBUG: Found TGZ match for TAR: {entry.Key}");
                            return entry.Key;
                        }
                    }
                }
                
                // If parent is a TGZ with single null entry, use empty string
                if (isParentTgz)
                {
                    Console.WriteLine($"DEBUG: Parent is TGZ with single entry - using empty string for TAR content");
                    return "";
                }
            }
            
            // Final fallback: if there's only one entry, use it
            if (parentArchive.Entries.Count() == 1)
            {
                var singleEntry = parentArchive.Entries.First();
                Console.WriteLine($"DEBUG: Single entry fallback: using '{singleEntry.Key ?? "empty"}'");
                return singleEntry.Key ?? "";
            }
            
            Console.WriteLine($"DEBUG: No match found for '{childBaseName}' - all fallbacks exhausted");
            return string.Empty;
        }

        public void CleanUp()
        {
            // Clean up temporary files
            // ... existing code ...
        }
    }

    public class ArchiveItem
    {
        public string Name { get; set; } = string.Empty;
        public long Size { get; set; }
        public bool IsDirectory { get; set; }
        public bool IsArchive { get; set; }
        public int Index { get; set; }
        public string FullPath { get; set; } = string.Empty;
    }
    
    public class ArchiveLevel
    {
        public IArchive Archive { get; set; } = null!;
        public string Path { get; set; } = string.Empty;
    }
    
    public class FileMonitor
    {
        public string FilePath { get; private set; }
        public DateTime LastWriteTime { get; private set; }
        
        public FileMonitor(string path, DateTime lastWriteTime)
        {
            FilePath = path;
            LastWriteTime = lastWriteTime;
        }
        
        public bool HasChanged()
        {
            if (!File.Exists(FilePath))
                return false;

            DateTime currentWriteTime = File.GetLastWriteTime(FilePath);
            return currentWriteTime > LastWriteTime;
        }
    }


}
