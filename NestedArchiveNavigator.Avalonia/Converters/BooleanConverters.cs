using Avalonia.Data.Converters;
using Avalonia.Media;
using System;
using System.Globalization;

namespace NestedArchiveNavigator.Avalonia.Converters
{
    public static class BoolConverters
    {
        public static readonly IValueConverter Not = new BooleanNotConverter();
        
        public static readonly IValueConverter BoolToFontWeight = 
            new FuncValueConverter<bool, FontWeight>(b => b ? FontWeight.Bold : FontWeight.Normal);
            
        public static readonly IValueConverter BoolToForeground = 
            new FuncValueConverter<bool, IBrush>(b => b ? Brushes.Blue : Brushes.Black);
    }

    public class BooleanNotConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }
}
