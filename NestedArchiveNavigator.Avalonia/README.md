# Nested Archive Navigator (Cross-Platform)

A modern, cross-platform file manager application built with **Avalonia UI** and **SharpCompress** that allows you to navigate nested archives like folders. Works on **macOS**, **Windows**, and **Linux**.

## 🚀 Features

✅ **Cross-Platform**: Runs on macOS, Windows, and Linux
✅ **Modern UI**: Built with Avalonia UI and Fluent Design
✅ **Pure .NET**: No native dependencies - uses SharpCompress library
✅ **Nested Navigation**: Navigate through archives within archives seamlessly
✅ **Multiple Formats**: Supports 7z, zip, rar, tar, gz, tgz, bz2, xz, lzma
✅ **File Operations**: Extract and open files with default applications
✅ **Drag & Drop**: Drop archive files to open them
✅ **MVVM Architecture**: Clean, maintainable code structure

## 🏗️ Architecture

### Technology Stack
- **UI Framework**: Avalonia UI 11.0
- **Archive Library**: SharpCompress (pure .NET)
- **MVVM Framework**: CommunityToolkit.Mvvm
- **Target Framework**: .NET 8.0

### Key Components
1. **CrossPlatformArchiveNavigator** - Core navigation logic using SharpCompress
2. **MainWindowViewModel** - MVVM view model with commands and data binding
3. **MainWindow** - Avalonia UI with modern design
4. **ArchiveItem** - Data model for files and folders

## 🛠️ Building

### Prerequisites
- .NET 8.0 SDK or later
- Any OS: macOS, Windows, or Linux

### Build Commands
```bash
cd NestedArchiveNavigator.Avalonia
dotnet restore
dotnet build
```

### Run the Application
```bash
dotnet run
```

## 📦 Publishing

### For Current Platform
```bash
dotnet publish -c Release
```

### For Specific Platforms
```bash
# Windows
dotnet publish -c Release -r win-x64 --self-contained

# macOS
dotnet publish -c Release -r osx-x64 --self-contained

# Linux
dotnet publish -c Release -r linux-x64 --self-contained
```

## 🎯 Usage

1. **Launch** the application
2. **Open Archive**: Click the "📁 Open Archive" button or drag & drop an archive file
3. **Navigate**: Double-click folders or archives to drill down
4. **Back Navigation**: Use the "← Back" button to go up levels
5. **Open Files**: Double-click files to extract and open them
6. **Status**: Monitor operations in the status bar

## 🔧 Supported Archive Formats

- **7-Zip**: .7z
- **ZIP**: .zip
- **RAR**: .rar
- **TAR**: .tar
- **GZIP**: .gz, .tgz
- **BZIP2**: .bz2
- **XZ**: .xz
- **LZMA**: .lzma

## 🆚 Comparison with Windows Version

| Feature | Windows (WinForms) | Cross-Platform (Avalonia) |
|---------|-------------------|---------------------------|
| **UI Framework** | Windows Forms | Avalonia UI |
| **Archive Library** | 7-Zip COM | SharpCompress |
| **Platforms** | Windows only | Windows, macOS, Linux |
| **Dependencies** | 7z.dll required | Pure .NET |
| **Design** | Classic Windows | Modern Fluent |
| **Performance** | Faster (native) | Good (managed) |

## 🔮 Future Enhancements

1. **Archive Modification**: Update files back into archives
2. **File Preview**: Built-in preview for common file types
3. **Search**: Find files within nested archives
4. **Bookmarks**: Save frequently accessed archive locations
5. **Themes**: Dark/Light mode support
6. **Plugins**: Extensible architecture for custom formats

## 🐛 Known Limitations

1. **Read-Only**: Currently only supports reading archives
2. **Large Archives**: May be slower with very large archives
3. **Some RAR Features**: Limited by SharpCompress capabilities
4. **Memory Usage**: Keeps archive objects in memory during navigation

## 🤝 Contributing

This cross-platform version demonstrates modern .NET development practices:
- Clean MVVM architecture
- Dependency injection ready
- Async/await patterns
- Cross-platform UI design

Feel free to contribute improvements or additional features!

## 📄 License

This is a demonstration project showing how to build cross-platform archive navigation tools.
