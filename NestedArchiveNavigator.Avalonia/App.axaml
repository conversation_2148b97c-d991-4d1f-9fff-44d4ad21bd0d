<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:converters="using:NestedArchiveNavigator.Avalonia.Converters"
             x:Class="NestedArchiveNavigator.Avalonia.App"
             RequestedThemeVariant="Dark">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.Styles>
        <FluentTheme />
        <StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Fluent.xaml"/>

        <!-- Custom styles -->
        <Style Selector="Button">
            <Setter Property="Margin" Value="2" />
            <Setter Property="Padding" Value="8,4" />
        </Style>

        <Style Selector="DataGrid">
            <Setter Property="GridLinesVisibility" Value="Horizontal" />
            <Setter Property="HeadersVisibility" Value="Column" />
        </Style>
    </Application.Styles>

    <Application.Resources>
        <!-- Resources will be added here if needed -->
    </Application.Resources>
</Application>
