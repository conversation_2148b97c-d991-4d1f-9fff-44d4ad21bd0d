#!/bin/bash

echo "🚀 Nested Archive Navigator (Cross-Platform)"
echo "============================================="

# Check if .NET is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET SDK not found. Please install .NET 8.0 or later."
    echo "   Download from: https://dotnet.microsoft.com/download"
    exit 1
fi

# Check .NET version
DOTNET_VERSION=$(dotnet --version)
echo "✅ Found .NET version: $DOTNET_VERSION"

# Build and run
echo "🔨 Building application..."
dotnet build -c Release

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    echo "🎯 Launching application..."
    dotnet run -c Release
else
    echo "❌ Build failed!"
    exit 1
fi
