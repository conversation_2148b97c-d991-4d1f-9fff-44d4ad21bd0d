<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:NestedArchiveNavigator.Avalonia.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"

        xmlns:converters="using:NestedArchiveNavigator.Avalonia.Converters"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="NestedArchiveNavigator.Avalonia.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Title="Archive Navigator Pro"
        Width="1200" Height="800"
        Background="#F8F9FA"
        Foreground="#212529"
        WindowStartupLocation="CenterScreen">

    <Design.DataContext>
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Window.Styles>
        <!-- COMPLETE BUTTON TEMPLATE OVERRIDE -->
        <Style Selector="Button">
            <Setter Property="Template">
                <ControlTemplate>
                    <Border Name="PART_Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter Name="PART_ContentPresenter"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        Foreground="{TemplateBinding Foreground}"
                                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter>
        </Style>

        <!-- FORCE WHITE TEXT ON ALL BUTTON HOVERS -->
        <Style Selector="Button:pointerover">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button:pointerover /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- Premium Button Styles -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#005A9B"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style Selector="Button.primary:pointerover">
            <Setter Property="Background" Value="#1E88E5"/>
            <Setter Property="BorderBrush" Value="#1565C0"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.primary:pointerover /template/ ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.primary:pressed">
            <Setter Property="Background" Value="#1565C0"/>
        </Style>

        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#5A6268"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style Selector="Button.secondary:pointerover">
            <Setter Property="Background" Value="#495057"/>
            <Setter Property="BorderBrush" Value="#343A40"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.secondary:pointerover /template/ ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.success">
            <Setter Property="Background" Value="#28A745"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#1E7E34"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style Selector="Button.success:pointerover">
            <Setter Property="Background" Value="#34CE57"/>
            <Setter Property="BorderBrush" Value="#28A745"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.success:pointerover /template/ ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- Premium TextBlock Styles -->
        <Style Selector="TextBlock.title">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
        </Style>

        <Style Selector="TextBlock.subtitle">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#34495E"/>
        </Style>

        <Style Selector="TextBlock.body">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="#5D6D7E"/>
        </Style>

        <!-- DataGrid Styling -->
        <Style Selector="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#212529"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F1F3F4"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="CanUserReorderColumns" Value="True"/>
            <Setter Property="CanUserResizeColumns" Value="True"/>
            <Setter Property="CanUserSortColumns" Value="True"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style Selector="DataGridColumnHeader">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="Foreground" Value="#495057"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="0,0,1,1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="13"/>
        </Style>

        <Style Selector="DataGridRow">
            <Setter Property="Foreground" Value="#212529"/>
        </Style>

        <Style Selector="DataGridCell">
            <Setter Property="Foreground" Value="#212529"/>
            <Setter Property="Padding" Value="12,8"/>
        </Style>
    </Window.Styles>

    <DockPanel>
        <!-- Premium Toolbar -->
        <Border DockPanel.Dock="Top"
                Background="White"
                BorderBrush="#E1E5E9"
                BorderThickness="0,0,0,1"
                Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Navigation and Action Buttons -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="8">
                    <Button Name="BackButton"
                            Command="{Binding NavigateBackCommand}"
                            IsEnabled="{Binding CanNavigateBack}"
                            Classes="secondary"
                            Content="← Back" />

                    <Button Name="ForwardButton"
                            Command="{Binding NavigateForwardCommand}"
                            IsEnabled="{Binding CanNavigateForward}"
                            Classes="secondary"
                            Content="Forward →" />

                    <Separator Margin="8,0" />

                    <Button Name="OpenArchiveButton"
                            Command="{Binding OpenArchiveCommand}"
                            Classes="primary"
                            Content="📁 Open Archive" />
                    <Button Name="CheckForModificationsButton"
                            Command="{Binding CheckForModificationsCommand}"
                            Classes="primary"
                            Content="💾 Check for Modifications" />
                    
                    <Button Name="EditFileButton"
                            Command="{Binding EditFileCommand}"
                            CommandParameter="{Binding SelectedItem}"
                            Classes="success"
                            Content="✏️ Edit Selected File"
                            IsEnabled="{Binding SelectedItem, Converter={x:Static ObjectConverters.IsNotNull}}" />
                </StackPanel>

                <!-- Path Display -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                    <TextBlock Text="Location:"
                               Classes="subtitle"
                               VerticalAlignment="Center"
                               Margin="0,0,12,0" />
                    <Border Background="#F8F9FA"
                            BorderBrush="#DEE2E6"
                            BorderThickness="1"
                            CornerRadius="4"
                            Padding="12,8"
                            MinWidth="300">
                        <TextBlock Text="{Binding CurrentPath}"
                                   Classes="body"
                                   TextTrimming="CharacterEllipsis"
                                   ToolTip.Tip="{Binding CurrentPath}" />
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Premium Status Bar -->
        <Border DockPanel.Dock="Bottom"
                Background="White"
                BorderBrush="#E1E5E9"
                BorderThickness="0,1,0,0"
                Padding="16,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="{Binding StatusText}"
                           Classes="body"
                           VerticalAlignment="Center" />

                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="16">
                    <TextBlock Text="Ready"
                               Classes="body"
                               Foreground="#28A745"
                               VerticalAlignment="Center" />
                    <TextBlock Text="Archive Navigator Pro v1.0"
                               Classes="body"
                               Foreground="#6C757D"
                               VerticalAlignment="Center" />
                </StackPanel>
            </Grid>
        </Border>

        <!-- Premium File List -->
        <Border Background="White"
                BorderBrush="#E1E5E9"
                BorderThickness="1"
                CornerRadius="8"
                Margin="16">
            <DataGrid Name="FileGrid"
                      ItemsSource="{Binding Items}"
                      SelectedItem="{Binding SelectedItem}"
                      IsReadOnly="True"
                      CanUserResizeColumns="True"
                      CanUserSortColumns="True"
                      AutoGenerateColumns="False"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column">

                <DataGrid.ContextFlyout>
                    <MenuFlyout>
                        <MenuItem Header="📂 Open/Navigate" Command="{Binding ItemDoubleClickCommand}" CommandParameter="{Binding SelectedItem}"/>
                        <MenuItem Header="✏️ Edit File" Command="{Binding EditFileCommand}" CommandParameter="{Binding SelectedItem}"/>
                        <Separator/>
                        <MenuItem Header="🔍 Properties" IsEnabled="False"/>
                    </MenuFlyout>
                </DataGrid.ContextFlyout>
                <DataGrid.Columns>
                    <DataGridTemplateColumn Header="Icon" Width="50">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Icon}" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    <DataGridTextColumn Header="Name" Binding="{Binding Name}" Width="*" />
                    <DataGridTextColumn Header="Size" Binding="{Binding Size}" Width="120" />
                    <DataGridTextColumn Header="Type" Binding="{Binding Type}" Width="300" />
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </DockPanel>
</Window>
