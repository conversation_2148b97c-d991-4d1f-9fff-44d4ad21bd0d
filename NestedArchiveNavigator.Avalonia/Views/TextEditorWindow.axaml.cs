using System;
using System.IO;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using NestedArchiveNavigator.Avalonia.ViewModels;

namespace NestedArchiveNavigator.Avalonia.Views
{
    public partial class TextEditorWindow : Window
    {
        private string _filePath = string.Empty;
        private string _originalContent = string.Empty;
        private bool _hasUnsavedChanges = false;
        private ArchiveItemViewModel? _archiveItem;
        private Func<ArchiveItemViewModel, string, Task>? _saveCallback;

        public TextEditorWindow()
        {
            InitializeComponent();
            
            // Set up keyboard shortcuts
            KeyDown += OnKeyDown;
            
            // Set up window closing behavior
            Closing += OnWindowClosing;
            
            // Ensure proper window display for modal dialog
            MinWidth = 600;
            MinHeight = 400;
            ShowInTaskbar = true;
            CanResize = true;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            
            // macOS specific settings
            SystemDecorations = SystemDecorations.Full;
            
            Console.WriteLine("DEBUG: TextEditorWindow constructor completed");
        }

        public async Task LoadFile(string filePath, ArchiveItemViewModel archiveItem, Func<ArchiveItemViewModel, string, Task> saveCallback)
        {
            try
            {
                _filePath = filePath;
                _archiveItem = archiveItem;
                _saveCallback = saveCallback;
                
                Console.WriteLine($"DEBUG: TextEditorWindow loading file: {filePath}");
                
                if (File.Exists(filePath))
                {
                    _originalContent = await File.ReadAllTextAsync(filePath);
                    TextEditor.Text = _originalContent;
                    
                    FileNameLabel.Text = archiveItem.Name;
                    Title = $"Archive Navigator Pro - Editing {archiveItem.Name}";
                    
                    StatusLabel.Text = "File loaded successfully";
                    UpdateStats();
                    
                    Console.WriteLine($"DEBUG: File loaded, content length: {_originalContent.Length}");
                }
                else
                {
                    StatusLabel.Text = "Error: File not found";
                    Console.WriteLine($"ERROR: File not found: {filePath}");
                }
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"Error loading file: {ex.Message}";
                Console.WriteLine($"ERROR: Failed to load file: {ex.Message}");
            }
        }

        private void OnKeyDown(object? sender, KeyEventArgs e)
        {
            // Ctrl+S to save
            if (e.Key == Key.S && e.KeyModifiers.HasFlag(KeyModifiers.Control))
            {
                _ = SaveFile();
                e.Handled = true;
            }
            // Ctrl+W to close
            else if (e.Key == Key.W && e.KeyModifiers.HasFlag(KeyModifiers.Control))
            {
                Close();
                e.Handled = true;
            }
        }

        private void OnTextChanged(object? sender, TextChangedEventArgs e)
        {
            _hasUnsavedChanges = TextEditor.Text != _originalContent;
            
            if (_hasUnsavedChanges)
            {
                StatusLabel.Text = "Modified";
                if (!Title.EndsWith(" *"))
                    Title += " *";
            }
            else
            {
                StatusLabel.Text = "Saved";
                if (Title.EndsWith(" *"))
                    Title = Title.Substring(0, Title.Length - 2);
            }
            
            UpdateStats();
        }

        private void UpdateStats()
        {
            string text = TextEditor.Text ?? "";
            CharCountLabel.Text = $"{text.Length} characters";
            
            int lineCount = string.IsNullOrEmpty(text) ? 1 : text.Split('\n').Length;
            LineCountLabel.Text = $"{lineCount} line{(lineCount == 1 ? "" : "s")}";
        }

        private async void OnSaveClick(object? sender, RoutedEventArgs e)
        {
            await SaveFile();
        }

        private async void OnSaveAndCloseClick(object? sender, RoutedEventArgs e)
        {
            if (await SaveFile())
            {
                Close();
            }
        }

        private void OnCancelClick(object? sender, RoutedEventArgs e)
        {
            Close();
        }

        private async Task<bool> SaveFile()
        {
            try
            {
                if (string.IsNullOrEmpty(_filePath) || _archiveItem == null || _saveCallback == null)
                {
                    StatusLabel.Text = "Error: Cannot save - missing file information";
                    return false;
                }

                StatusLabel.Text = "Saving...";
                Console.WriteLine($"DEBUG: Saving file to: {_filePath}");

                // Write the content to the temporary file
                await File.WriteAllTextAsync(_filePath, TextEditor.Text);
                
                // Call the save callback to update the archive
                await _saveCallback(_archiveItem, _filePath);

                _originalContent = TextEditor.Text;
                _hasUnsavedChanges = false;
                
                StatusLabel.Text = "Saved successfully";
                
                if (Title.EndsWith(" *"))
                    Title = Title.Substring(0, Title.Length - 2);

                Console.WriteLine($"DEBUG: File saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                StatusLabel.Text = $"Error saving: {ex.Message}";
                Console.WriteLine($"ERROR: Failed to save file: {ex.Message}");
                return false;
            }
        }

        private async void OnWindowClosing(object? sender, WindowClosingEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                // TODO: Show confirmation dialog
                // For now, just allow closing
                Console.WriteLine("DEBUG: Window closing with unsaved changes");
            }
        }
    }
}
