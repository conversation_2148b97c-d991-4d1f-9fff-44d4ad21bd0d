using System;
using System.Linq;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Platform.Storage;
using NestedArchiveNavigator.Avalonia.ViewModels;
using CommunityToolkit.Mvvm.Input;
using System.Threading.Tasks;

namespace NestedArchiveNavigator.Avalonia.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            Console.WriteLine($"DEBUG: MainWindow constructor - DataContext type: {DataContext?.GetType().Name ?? "null"}");

            // Handle file drop
            AddHandler(DragDrop.DropEvent, OnDrop);
            AddHandler(DragDrop.DragOverEvent, OnDragOver);
            
            // Handle DataGrid double-click directly with better event handling
            FileGrid.DoubleTapped += OnFileGridDoubleTapped;
            FileGrid.KeyDown += OnFileGridKeyDown;
            
            Console.WriteLine("DEBUG: Event handlers attached");
        }

        protected override void OnLoaded(RoutedEventArgs e)
        {
            base.OnLoaded(e);

            Console.WriteLine($"DEBUG: OnLoaded - DataContext type: {DataContext?.GetType().Name ?? "null"}");

            // Set up the file picker functionality
            if (DataContext is MainWindowViewModel viewModel)
            {
                Console.WriteLine("DEBUG: DataContext is MainWindowViewModel, setting up file picker");
                viewModel.SetFilePickerProvider(async () =>
                {
                    var files = await StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
                    {
                        Title = "Open Archive",
                        AllowMultiple = false,
                        FileTypeFilter = new[]
                        {
                            new FilePickerFileType("Archive Files")
                            {
                                Patterns = new[] { "*.7z", "*.zip", "*.rar", "*.tar", "*.gz", "*.tgz", "*.bz2", "*.xz", "*.lzma" }
                            },
                            new FilePickerFileType("All Files")
                            {
                                Patterns = new[] { "*.*" }
                            }
                        }
                    });

                    return files.Count > 0 ? files[0].Path.LocalPath : null;
                });
            }
            else
            {
                Console.WriteLine("DEBUG: DataContext is NOT MainWindowViewModel!");
            }
        }

        private void OnDragOver(object? sender, DragEventArgs e)
        {
            if (e.Data.GetFiles()?.Any() == true)
            {
                e.DragEffects = DragDropEffects.Copy;
            }
            else
            {
                e.DragEffects = DragDropEffects.None;
            }
        }

        private async void OnDrop(object? sender, DragEventArgs e)
        {
            var files = e.Data.GetFiles();
            if (files?.Any() == true)
            {
                var firstFile = files.First().Path.LocalPath;
                if (DataContext is MainWindowViewModel vm)
                {
                    await vm.OpenArchiveFile(firstFile);
                }
            }
        }

        private async void OnFileGridDoubleTapped(object? sender, TappedEventArgs e)
        {
            Console.WriteLine("DEBUG: DataGrid double-tapped!");
            
            if (DataContext is MainWindowViewModel vm && vm.SelectedItem != null)
            {
                Console.WriteLine($"DEBUG: Selected item: {vm.SelectedItem.Name}");
                
                try
                {
                    Console.WriteLine("DEBUG: Calling ItemDoubleClickCommand...");
                    await vm.ItemDoubleClickCommand.ExecuteAsync(vm.SelectedItem);
                    Console.WriteLine("DEBUG: ItemDoubleClickCommand completed");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DEBUG: Error in double-click handler: {ex.Message}");
                    Console.WriteLine($"DEBUG: Stack trace: {ex.StackTrace}");
                }
            }
            else
            {
                Console.WriteLine("DEBUG: No action - ViewModel or SelectedItem is null");
            }
        }

        private async void OnFileGridKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                Console.WriteLine("DEBUG: Enter key pressed on DataGrid");
                
                if (DataContext is MainWindowViewModel vm && vm.SelectedItem != null)
                {
                    Console.WriteLine($"DEBUG: Selected item for Enter: {vm.SelectedItem.Name}");
                    
                    try
                    {
                        await vm.ItemDoubleClickCommand.ExecuteAsync(vm.SelectedItem);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"DEBUG: Error in Enter key handler: {ex.Message}");
                    }
                }
            }
        }
    }
}
