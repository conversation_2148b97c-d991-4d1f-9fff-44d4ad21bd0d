<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="600"
        x:Class="NestedArchiveNavigator.Avalonia.Views.TextEditorWindow"
        Title="Archive Navigator Pro - Text Editor"
        Width="900" Height="700"
        Background="#F8F9FA"
        WindowStartupLocation="CenterScreen"
        ShowInTaskbar="True"
        Topmost="False"
        CanResize="True">

    <Window.Styles>
        <!-- COMPLETE BUTTON TEMPLATE OVERRIDE -->
        <Style Selector="Button">
            <Setter Property="Template">
                <ControlTemplate>
                    <Border Name="PART_Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter Name="PART_ContentPresenter"
                                        Content="{TemplateBinding Content}"
                                        ContentTemplate="{TemplateBinding ContentTemplate}"
                                        Foreground="{TemplateBinding Foreground}"
                                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                </ControlTemplate>
            </Setter>
        </Style>

        <!-- FORCE WHITE TEXT ON ALL BUTTON HOVERS -->
        <Style Selector="Button:pointerover">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button:pointerover /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- Premium Button Styles -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#005A9B"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
        
        <Style Selector="Button.primary:pointerover">
            <Setter Property="Background" Value="#1E88E5"/>
            <Setter Property="BorderBrush" Value="#1565C0"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.primary:pointerover /template/ ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#5A6268"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
        
        <Style Selector="Button.secondary:pointerover">
            <Setter Property="Background" Value="#495057"/>
            <Setter Property="BorderBrush" Value="#343A40"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.secondary:pointerover /template/ ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <Style Selector="Button.success">
            <Setter Property="Background" Value="#28A745"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#1E7E34"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
        
        <Style Selector="Button.success:pointerover">
            <Setter Property="Background" Value="#34CE57"/>
            <Setter Property="BorderBrush" Value="#28A745"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.success:pointerover /template/ ContentPresenter">
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- TextBox Styles - COMPLETELY DISABLE HOVER EFFECTS -->
        <Style Selector="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
        </Style>

        <!-- FORCE NO HOVER EFFECTS ON TEXTBOX -->
        <Style Selector="TextBox:pointerover">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
        </Style>

        <!-- DISABLE HOVER ON TEXTBOX TEMPLATE PARTS -->
        <Style Selector="TextBox:pointerover /template/ Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
        </Style>

        <Style Selector="TextBox:pointerover /template/ ScrollViewer">
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style Selector="TextBox:pointerover /template/ TextPresenter">
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style Selector="TextBox:focus">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#2C3E50"/>
            <Setter Property="BorderBrush" Value="#007ACC"/>
        </Style>
    </Window.Styles>

    <DockPanel>
        <!-- Toolbar -->
        <Border DockPanel.Dock="Top" 
                Background="White" 
                BorderBrush="#E1E5E9" 
                BorderThickness="0,0,0,1"
                Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- File Info -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📄" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Name="FileNameLabel" 
                               Text="filename.txt" 
                               FontWeight="SemiBold" 
                               FontSize="14" 
                               Foreground="#2C3E50"
                               VerticalAlignment="Center"/>
                </StackPanel>
                
                <!-- Status -->
                <TextBlock Grid.Column="1" 
                           Name="StatusLabel"
                           Text="Ready" 
                           FontSize="13" 
                           Foreground="#5D6D7E"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
                
                <!-- Action Buttons -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="8">
                    <Button Name="SaveButton" 
                            Classes="success"
                            Content="💾 Save"
                            Click="OnSaveClick" />
                    
                    <Button Name="SaveAndCloseButton" 
                            Classes="primary"
                            Content="💾 Save and Close"
                            Click="OnSaveAndCloseClick" />
                    
                    <Button Name="CancelButton" 
                            Classes="secondary"
                            Content="❌ Cancel"
                            Click="OnCancelClick" />
                </StackPanel>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border DockPanel.Dock="Bottom" 
                Background="White" 
                BorderBrush="#E1E5E9" 
                BorderThickness="0,1,0,0"
                Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0"
                           Name="InfoLabel"
                           Text="Use Ctrl+S to save, Ctrl+W to close" 
                           FontSize="12"
                           Foreground="#6C757D"
                           VerticalAlignment="Center" />
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="16">
                    <TextBlock Name="CharCountLabel"
                               Text="0 characters" 
                               FontSize="12"
                               Foreground="#6C757D"
                               VerticalAlignment="Center" />
                    <TextBlock Name="LineCountLabel"
                               Text="1 line" 
                               FontSize="12"
                               Foreground="#6C757D"
                               VerticalAlignment="Center" />
                </StackPanel>
            </Grid>
        </Border>

        <!-- Text Editor -->
        <Border Background="White" 
                BorderBrush="#E1E5E9" 
                BorderThickness="1"
                CornerRadius="8"
                Margin="16">
            <TextBox Name="TextEditor"
                     AcceptsReturn="True"
                     AcceptsTab="True"
                     TextWrapping="NoWrap"
                     FontFamily="Monaco,Consolas,Courier New,monospace"
                     FontSize="13"
                     Background="White"
                     Foreground="#2C3E50"
                     BorderThickness="0"
                     Padding="16"
                     ScrollViewer.HorizontalScrollBarVisibility="Auto"
                     ScrollViewer.VerticalScrollBarVisibility="Auto"
                     TextChanged="OnTextChanged" />
        </Border>
    </DockPanel>
</Window>
