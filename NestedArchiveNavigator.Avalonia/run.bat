@echo off
echo 🚀 Nested Archive Navigator (Cross-Platform)
echo =============================================

REM Check if .NET is installed
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ .NET SDK not found. Please install .NET 8.0 or later.
    echo    Download from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM Check .NET version
for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ Found .NET version: %DOTNET_VERSION%

REM Build and run
echo 🔨 Building application...
dotnet build -c Release

if %ERRORLEVEL% equ 0 (
    echo ✅ Build successful!
    echo 🎯 Launching application...
    dotnet run -c Release
) else (
    echo ❌ Build failed!
    pause
    exit /b 1
)
