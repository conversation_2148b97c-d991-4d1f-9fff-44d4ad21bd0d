# Archive Navigator Pro

A powerful, cross-platform archive file manager built with Avalonia UI and .NET 9. Navigate through nested archives like folders, with support for directory structures and a premium business-like interface.

![Archive Navigator Pro](https://img.shields.io/badge/Platform-.NET%209-blue)
![License](https://img.shields.io/badge/License-MIT-green)
![UI Framework](https://img.shields.io/badge/UI-Avalonia-purple)

## 🎯 **Current Implementation Status**

### ✅ **Fully Working Features**
- **Archive Navigation**: Complete nested archive browsing with back/forward buttons
- **File Manager UI**: Professional interface with file type icons, grid lines, and visual indicators
- **File Editing**: Right-click context menu for in-app editing (opens in system text editor)
- **File Monitoring**: Detects file changes and shows 7-Zip style save prompts
- **Immediate Save-Back**: Successfully saves changes to the current archive level
- **Cross-Platform**: Tested and working on macOS with proper file handling
- **Drag & Drop**: Archive and file handling support

### 🔄 **Partially Working Features**
- **Nested Save-Back**: Updates the immediate archive containing the modified file, but doesn't propagate changes back through all parent archive levels to the root archive

### ❌ **Known Limitations**
- **Full Nested Save-Back**: Changes to deeply nested files (e.g., `root.tgz → EMV.tar → zzNCR.tgz → zzNCR.tar → file.ini`) are saved to the immediate container but not propagated to the root archive
- **Complex Archive Structures**: The propagation logic needs refinement for complex nested relationships

### 📝 **Current Workflow**
- ✅ Browse deeply nested archives (any depth)
- ✅ Edit files in nested archives
- ✅ Save changes to immediate archive level
- ⚠️ Manual extraction/re-archiving needed for full nested save-back

## ✨ Features

### 🗂️ **Advanced Archive Navigation**
- **Nested Archive Support**: Navigate through archives within archives seamlessly
- **Directory Structure**: Proper hierarchical folder navigation within archives
- **Multiple Format Support**: 7z, ZIP, RAR, TAR, TGZ, BZ2, XZ, LZMA and more
- **Back/Forward Navigation**: Browser-like navigation with history

### 🎨 **Premium User Interface**
- **Business-Grade Design**: Professional, clean interface suitable for enterprise use
- **Modern Styling**: Rounded corners, hover effects, and premium color scheme
- **Visual File Indicators**: Icons for files, folders, and archives
- **Grid Lines**: Subtle horizontal lines for better readability
- **Responsive Layout**: Adapts to different window sizes

### 🚀 **Performance & Reliability**
- **Cross-Platform**: Runs on Windows, macOS, and Linux
- **Memory Efficient**: Smart caching and resource management
- **Error Handling**: Robust error recovery and user feedback
- **File Monitoring**: Track changes to extracted files

## 🛠️ **Technology Stack**

- **Framework**: .NET 9
- **UI Library**: Avalonia UI 11.x
- **Archive Library**: SharpCompress
- **Architecture**: MVVM with CommunityToolkit.Mvvm
- **Platform**: Cross-platform (Windows, macOS, Linux)

## 📋 **Requirements**

- .NET 9 SDK or later
- Operating System: Windows 10+, macOS 10.15+, or Linux with X11/Wayland

## 🚀 **Getting Started**

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/archive-navigator-pro.git
   cd archive-navigator-pro
   ```

2. **Restore dependencies**:
   ```bash
   dotnet restore
   ```

3. **Build the project**:
   ```bash
   dotnet build
   ```

4. **Run the application**:
   ```bash
   dotnet run
   ```

### Usage

1. **Open an Archive**: Click "📁 Open Archive" to select an archive file
2. **Navigate Directories**: Double-click folders to enter them
3. **Navigate Archives**: Double-click archive files to explore their contents
4. **Go Back**: Use the "← Back" button to navigate up directory levels or back to parent archives
5. **Go Forward**: Use the "Forward →" button to navigate forward in your history

## 🏗️ **Project Structure**

```
NestedArchiveNavigator.Avalonia/
├── Core/
│   └── CrossPlatformArchiveNavigator.cs    # Main navigation logic
├── ViewModels/
│   ├── MainWindowViewModel.cs              # Main window view model
│   └── ArchiveItemViewModel.cs             # Archive item view model
├── Views/
│   └── MainWindow.axaml                    # Main window UI
├── Models/
│   └── ArchiveItem.cs                      # Archive item model
└── Program.cs                              # Application entry point
```

## 🔧 **Key Features Explained**

### Directory Navigation
The application properly handles directory structures within archives:
- Files are organized within their respective folders
- No flattening of directory structure
- Breadcrumb navigation shows current location

### Archive-in-Archive Support
Navigate through nested archives seamlessly:
- TGZ files are automatically recognized and made navigable
- Temporary extraction with timestamp-based naming
- Automatic cleanup of temporary files

### Premium UI Design
- **Color Scheme**: Professional blues and grays
- **Button Styles**: Primary (blue), Secondary (gray), Success (green)
- **Typography**: Consistent font weights and sizes
- **Spacing**: Proper margins and padding throughout

## 🤝 **Contributing**

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 **Acknowledgments**

- [Avalonia UI](https://avaloniaui.net/) - Cross-platform .NET UI framework
- [SharpCompress](https://github.com/adamhathcock/sharpcompress) - Archive extraction library
- [CommunityToolkit.Mvvm](https://github.com/CommunityToolkit/dotnet) - MVVM helpers

## 📞 **Support**

If you encounter any issues or have questions, please [open an issue](https://github.com/yourusername/archive-navigator-pro/issues) on GitHub.

---

**Archive Navigator Pro** - Professional archive management made simple.
