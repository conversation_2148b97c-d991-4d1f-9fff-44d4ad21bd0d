#!/usr/bin/env python3
"""
Test script to reproduce duplicate issue in DEEP nested archives
This creates a 5-level deep nested structure to trigger the bug
"""

import os
import zipfile
import tempfile
import shutil
from pathlib import Path

def create_deep_nested_structure():
    """Create a 5-level deep nested archive structure"""
    print("🔧 Creating DEEP nested archive structure (5 levels)...")
    
    # Level 5: Deepest archive
    with zipfile.ZipFile('level5.zip', 'w') as zf:
        zf.writestr('deepest_file.txt', 'Content at the deepest level 5')
        zf.writestr('deep_config.ini', '[deep]\nlevel=5\ndeepest=true')
    
    # Level 4: Contains level5
    with zipfile.ZipFile('level4.zip', 'w') as zf:
        zf.writestr('level4_file.txt', 'Content at level 4')
        with open('level5.zip', 'rb') as f:
            zf.writestr('nested/level5.zip', f.read())
    
    # Level 3: Contains level4
    with zipfile.ZipFile('level3.zip', 'w') as zf:
        zf.writestr('level3_file.txt', 'Content at level 3')
        with open('level4.zip', 'rb') as f:
            zf.writestr('deeper/level4.zip', f.read())
    
    # Level 2: Contains level3
    with zipfile.ZipFile('level2.zip', 'w') as zf:
        zf.writestr('level2_file.txt', 'Content at level 2')
        zf.writestr('some_data.json', '{"level": 2, "contains": "level3"}')
        with open('level3.zip', 'rb') as f:
            zf.writestr('archives/level3.zip', f.read())
    
    # Level 1: Root archive contains level2
    with zipfile.ZipFile('deep_root.zip', 'w') as zf:
        zf.writestr('root_readme.txt', 'Root level of deep nested archive')
        zf.writestr('manifest.xml', '<?xml version="1.0"?><manifest><levels>5</levels></manifest>')
        with open('level2.zip', 'rb') as f:
            zf.writestr('level2.zip', f.read())
        zf.writestr('root_config.cfg', 'root_setting=enabled\ndeep_nesting=true')
    
    # Clean up intermediate files
    for level_file in ['level5.zip', 'level4.zip', 'level3.zip', 'level2.zip']:
        if os.path.exists(level_file):
            os.remove(level_file)
    
    print("✅ Deep nested archive structure created: deep_root.zip (5 levels deep)")
    return 'deep_root.zip'

def analyze_for_duplicates(archive_path, name="Archive", verbose=True):
    """Analyze archive for duplicates with detailed reporting"""
    if verbose:
        print(f"\n🔍 ANALYZING: {name}")
        print("-" * 60)
    
    try:
        with zipfile.ZipFile(archive_path, 'r') as zf:
            entries = zf.namelist()
            if verbose:
                print(f"📊 Total entries: {len(entries)}")
            
            # Check for exact duplicates
            entry_counts = {}
            duplicates = []
            
            for entry in entries:
                if entry in entry_counts:
                    entry_counts[entry] += 1
                    if entry not in duplicates:
                        duplicates.append(entry)
                else:
                    entry_counts[entry] = 1
            
            if duplicates:
                print(f"❌ DUPLICATES FOUND: {duplicates}")
                for dup in duplicates:
                    print(f"   '{dup}' appears {entry_counts[dup]} times")
                return duplicates
            else:
                if verbose:
                    print("✅ No duplicates detected")
                return []
                
    except Exception as ex:
        print(f"❌ Error analyzing {name}: {ex}")
        return []

def simulate_deep_save_back_with_debug():
    """Simulate save-back with deep nesting and detailed debugging"""
    print("\n🔄 Simulating DEEP nested save-back with extensive debugging...")
    
    root_archive = create_deep_nested_structure()
    
    # Initial analysis
    before_duplicates = analyze_for_duplicates(root_archive, "BEFORE Deep Save-Back")
    
    # Create working directory
    work_dir = tempfile.mkdtemp(prefix='DeepNested_')
    print(f"📁 Working directory: {work_dir}")
    
    try:
        # Simulate navigation down to level 5 and modification
        print("\n🔧 Simulating navigation to level 5 (deepest)...")
        
        # Extract level2 from root
        level2_path = os.path.join(work_dir, 'level2.zip')
        with zipfile.ZipFile(root_archive, 'r') as zf:
            with zf.open('level2.zip') as src:
                with open(level2_path, 'wb') as dst:
                    dst.write(src.read())
        
        # Extract level3 from level2
        level3_path = os.path.join(work_dir, 'level3.zip')
        with zipfile.ZipFile(level2_path, 'r') as zf:
            with zf.open('archives/level3.zip') as src:
                with open(level3_path, 'wb') as dst:
                    dst.write(src.read())
        
        # Extract level4 from level3
        level4_path = os.path.join(work_dir, 'level4.zip')
        with zipfile.ZipFile(level3_path, 'r') as zf:
            with zf.open('deeper/level4.zip') as src:
                with open(level4_path, 'wb') as dst:
                    dst.write(src.read())
        
        # Extract level5 from level4
        level5_path = os.path.join(work_dir, 'level5.zip')
        with zipfile.ZipFile(level4_path, 'r') as zf:
            with zf.open('nested/level5.zip') as src:
                with open(level5_path, 'wb') as dst:
                    dst.write(src.read())
        
        # Modify a file in level5 (deepest level)
        print("\n✏️  Modifying file at deepest level (level 5)...")
        level5_extract = os.path.join(work_dir, 'level5_extract')
        os.makedirs(level5_extract)
        
        with zipfile.ZipFile(level5_path, 'r') as zf:
            zf.extractall(level5_extract)
        
        # Modify the deepest file
        deepest_file = os.path.join(level5_extract, 'deepest_file.txt')
        with open(deepest_file, 'w') as f:
            f.write('MODIFIED CONTENT at deepest level 5\nThis change should propagate up 5 levels!')
        
        print("\n🔄 Starting propagation chain (5 levels up)...")
        
        # Step 1: Update level5
        print("\n📦 Step 1: Updating level5...")
        new_level5 = level5_path + '.new'
        with zipfile.ZipFile(new_level5, 'w') as zf:
            for root, dirs, files in os.walk(level5_extract):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, level5_extract)
                    print(f"   📄 Adding to level5: {arc_name}")
                    zf.write(file_path, arc_name)
        os.remove(level5_path)
        os.rename(new_level5, level5_path)
        
        # Step 2: Update level4 with modified level5
        print("\n📦 Step 2: Updating level4 with modified level5...")
        level4_extract = os.path.join(work_dir, 'level4_extract')
        os.makedirs(level4_extract)
        
        # Extract level4 and replace nested level5
        with zipfile.ZipFile(level4_path, 'r') as zf:
            for entry in zf.infolist():
                if not entry.is_dir():
                    entry_path = entry.filename
                    full_path = os.path.join(level4_extract, entry_path)
                    dir_path = os.path.dirname(full_path)
                    
                    if dir_path and not os.path.exists(dir_path):
                        os.makedirs(dir_path)
                    
                    if entry_path == 'nested/level5.zip':
                        # Replace with modified level5
                        print(f"   🔄 Replacing: {entry_path}")
                        shutil.copy2(level5_path, full_path)
                    else:
                        print(f"   📄 Extracting: {entry_path}")
                        with zf.open(entry_path) as src:
                            with open(full_path, 'wb') as dst:
                                dst.write(src.read())
        
        # Recreate level4 - POTENTIAL DUPLICATE SOURCE #1
        new_level4 = level4_path + '.new'
        with zipfile.ZipFile(new_level4, 'w') as zf:
            for root, dirs, files in os.walk(level4_extract):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, level4_extract)
                    arc_name = arc_name.replace('\\', '/')
                    print(f"   📄 Adding to level4: {arc_name}")
                    zf.write(file_path, arc_name)
        os.remove(level4_path)
        os.rename(new_level4, level4_path)
        
        # Check for duplicates after level4 update
        level4_dups = analyze_for_duplicates(level4_path, "Level4 after update", verbose=False)
        if level4_dups:
            print(f"⚠️  Duplicates found in level4: {level4_dups}")
        
        # Step 3: Update level3 with modified level4
        print("\n📦 Step 3: Updating level3 with modified level4...")
        level3_extract = os.path.join(work_dir, 'level3_extract')
        os.makedirs(level3_extract)
        
        with zipfile.ZipFile(level3_path, 'r') as zf:
            for entry in zf.infolist():
                if not entry.is_dir():
                    entry_path = entry.filename
                    full_path = os.path.join(level3_extract, entry_path)
                    dir_path = os.path.dirname(full_path)
                    
                    if dir_path and not os.path.exists(dir_path):
                        os.makedirs(dir_path)
                    
                    if entry_path == 'deeper/level4.zip':
                        print(f"   🔄 Replacing: {entry_path}")
                        shutil.copy2(level4_path, full_path)
                    else:
                        print(f"   📄 Extracting: {entry_path}")
                        with zf.open(entry_path) as src:
                            with open(full_path, 'wb') as dst:
                                dst.write(src.read())
        
        # Recreate level3 - POTENTIAL DUPLICATE SOURCE #2
        new_level3 = level3_path + '.new'
        with zipfile.ZipFile(new_level3, 'w') as zf:
            for root, dirs, files in os.walk(level3_extract):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, level3_extract)
                    arc_name = arc_name.replace('\\', '/')
                    print(f"   📄 Adding to level3: {arc_name}")
                    zf.write(file_path, arc_name)
        os.remove(level3_path)
        os.rename(new_level3, level3_path)
        
        # Check for duplicates after level3 update
        level3_dups = analyze_for_duplicates(level3_path, "Level3 after update", verbose=False)
        if level3_dups:
            print(f"⚠️  Duplicates found in level3: {level3_dups}")
        
        # Step 4: Update level2 with modified level3
        print("\n📦 Step 4: Updating level2 with modified level3...")
        level2_extract = os.path.join(work_dir, 'level2_extract')
        os.makedirs(level2_extract)
        
        with zipfile.ZipFile(level2_path, 'r') as zf:
            for entry in zf.infolist():
                if not entry.is_dir():
                    entry_path = entry.filename
                    full_path = os.path.join(level2_extract, entry_path)
                    dir_path = os.path.dirname(full_path)
                    
                    if dir_path and not os.path.exists(dir_path):
                        os.makedirs(dir_path)
                    
                    if entry_path == 'archives/level3.zip':
                        print(f"   🔄 Replacing: {entry_path}")
                        shutil.copy2(level3_path, full_path)
                    else:
                        print(f"   📄 Extracting: {entry_path}")
                        with zf.open(entry_path) as src:
                            with open(full_path, 'wb') as dst:
                                dst.write(src.read())
        
        # Recreate level2 - POTENTIAL DUPLICATE SOURCE #3
        new_level2 = level2_path + '.new'
        with zipfile.ZipFile(new_level2, 'w') as zf:
            for root, dirs, files in os.walk(level2_extract):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, level2_extract)
                    arc_name = arc_name.replace('\\', '/')
                    print(f"   📄 Adding to level2: {arc_name}")
                    zf.write(file_path, arc_name)
        os.remove(level2_path)
        os.rename(new_level2, level2_path)
        
        # Check for duplicates after level2 update
        level2_dups = analyze_for_duplicates(level2_path, "Level2 after update", verbose=False)
        if level2_dups:
            print(f"⚠️  Duplicates found in level2: {level2_dups}")
        
        # Step 5: Update root with modified level2
        print("\n📦 Step 5: Updating root with modified level2...")
        root_extract = os.path.join(work_dir, 'root_extract')
        os.makedirs(root_extract)
        
        with zipfile.ZipFile(root_archive, 'r') as zf:
            for entry in zf.infolist():
                if not entry.is_dir():
                    entry_path = entry.filename
                    full_path = os.path.join(root_extract, entry_path)
                    dir_path = os.path.dirname(full_path)
                    
                    if dir_path and not os.path.exists(dir_path):
                        os.makedirs(dir_path)
                    
                    if entry_path == 'level2.zip':
                        print(f"   🔄 Replacing: {entry_path}")
                        shutil.copy2(level2_path, full_path)
                    else:
                        print(f"   📄 Extracting: {entry_path}")
                        with zf.open(entry_path) as src:
                            with open(full_path, 'wb') as dst:
                                dst.write(src.read())
        
        # Recreate root - POTENTIAL DUPLICATE SOURCE #4
        new_root = root_archive + '.new'
        with zipfile.ZipFile(new_root, 'w') as zf:
            for root, dirs, files in os.walk(root_extract):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, root_extract)
                    arc_name = arc_name.replace('\\', '/')
                    print(f"   📄 Adding to root: {arc_name}")
                    zf.write(file_path, arc_name)
        os.remove(root_archive)
        os.rename(new_root, root_archive)
        
    finally:
        # Clean up working directory
        shutil.rmtree(work_dir)
    
    # Final analysis
    print("\n🔍 FINAL ANALYSIS:")
    after_duplicates = analyze_for_duplicates(root_archive, "AFTER Deep Save-Back")
    
    # Compare results
    if after_duplicates and not before_duplicates:
        print(f"\n❌ DUPLICATES INTRODUCED: {after_duplicates}")
        print("This confirms the deep nesting duplicate bug!")
        return False
    elif len(after_duplicates) > len(before_duplicates):
        print(f"\n❌ MORE DUPLICATES THAN BEFORE:")
        print(f"   Before: {len(before_duplicates)} duplicates")
        print(f"   After:  {len(after_duplicates)} duplicates")
        print("This confirms the deep nesting duplicate bug!")
        return False
    else:
        print("\n✅ No new duplicates introduced")
        return True

def main():
    """Main test function"""
    print("🚀 Testing DEEP Nested Archive Duplicate Issue")
    print("=" * 70)
    
    try:
        success = simulate_deep_save_back_with_debug()
        
        print(f"\n🎯 FINAL RESULT:")
        print("=" * 50)
        if success:
            print("✅ Deep nested save-back test PASSED")
            print("   No duplicates detected in 5-level deep structure")
        else:
            print("❌ Deep nested save-back test FAILED")
            print("   Duplicates detected - confirms the bug exists!")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        for file in ['deep_root.zip']:
            if os.path.exists(file):
                os.remove(file)

if __name__ == "__main__":
    success = main()
    print(f"\nTest result: {'PASS' if success else 'FAIL'}")
    exit(0 if success else 1) 