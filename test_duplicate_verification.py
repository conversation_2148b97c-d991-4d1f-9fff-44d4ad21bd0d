#!/usr/bin/env python3
"""
Test script to specifically verify duplicate entries during save-back operations
This tests various scenarios that might cause duplicates in Archive Navigator Pro
"""

import os
import time
import zipfile
import tempfile
import shutil
from pathlib import Path

def create_complex_test_archive():
    """Create a more complex test archive that might trigger duplicates"""
    print("🔧 Creating complex test archive...")
    
    # Create test content
    with zipfile.ZipFile('test_complex.zip', 'w') as zf:
        # Add multiple files with similar paths
        zf.writestr('file1.txt', 'Content of file1')
        zf.writestr('folder/file2.txt', 'Content of file2')
        zf.writestr('folder/subfolder/file3.txt', 'Content of file3')
        
        # Add a nested archive
        inner_content = 'Inner archive content'
        with zipfile.ZipFile('inner.zip', 'w') as inner_zf:
            inner_zf.writestr('inner_file.txt', inner_content)
        
        with open('inner.zip', 'rb') as inner_file:
            zf.writestr('folder/inner.zip', inner_file.read())
        
        os.remove('inner.zip')
    
    print("✅ Complex test archive created: test_complex.zip")
    return 'test_complex.zip'

def detailed_archive_analysis(archive_path, name="Archive"):
    """Detailed analysis with enhanced duplicate detection"""
    print(f"\n🔍 DETAILED ANALYSIS: {name}")
    print("-" * 50)
    
    try:
        with zipfile.ZipFile(archive_path, 'r') as zf:
            entries = zf.namelist()
            print(f"📊 Total entries: {len(entries)}")
            
            # Enhanced duplicate detection
            entry_counts = {}
            exact_duplicates = []
            path_normalized_duplicates = []
            
            for entry in entries:
                # Count exact matches
                if entry in entry_counts:
                    entry_counts[entry] += 1
                    if entry not in exact_duplicates:
                        exact_duplicates.append(entry)
                else:
                    entry_counts[entry] = 1
                
                # Check for path normalization issues
                normalized = entry.replace('\\', '/').lower().strip()
                if normalized in [e.replace('\\', '/').lower().strip() for e in entries if e != entry]:
                    if entry not in path_normalized_duplicates:
                        path_normalized_duplicates.append(entry)
            
            if exact_duplicates:
                print(f"❌ EXACT DUPLICATES FOUND: {exact_duplicates}")
                for dup in exact_duplicates:
                    print(f"   '{dup}' appears {entry_counts[dup]} times")
            
            if path_normalized_duplicates:
                print(f"⚠️  PATH NORMALIZATION DUPLICATES: {path_normalized_duplicates}")
            
            if not exact_duplicates and not path_normalized_duplicates:
                print("✅ No duplicates detected")
            
            # List all entries with details
            print(f"\n📋 All entries:")
            for i, entry in enumerate(entries):
                info = zf.getinfo(entry)
                entry_type = "📁 DIR" if entry.endswith('/') else "📄 FILE"
                print(f"   {i+1:2d}. {entry_type} '{entry}' ({info.file_size} bytes)")
                
    except Exception as ex:
        print(f"❌ Error analyzing {name}: {ex}")

def simulate_archiveupdateservice_logic():
    """Simulate the ArchiveUpdateService.UpdateArchive logic to test for duplicates"""
    print("\n🔄 Simulating ArchiveUpdateService logic...")
    
    archive_path = create_complex_test_archive()
    
    # Analyze BEFORE
    detailed_archive_analysis(archive_path, "BEFORE ArchiveUpdateService Update")
    
    # Simulate the ArchiveUpdateService.UpdateArchive process
    print("\n🔧 Simulating ArchiveUpdateService.UpdateArchive process...")
    
    # Define updates (like the C# code would)
    updates = [
        ('file1.txt', b'UPDATED CONTENT FOR FILE1\nThis file has been modified via save-back'),
        ('folder/file2.txt', b'UPDATED CONTENT FOR FILE2\nThis file has also been modified')
    ]
    
    temp_path = archive_path + '.temp'
    
    # Simulate the C# ArchiveUpdateService logic
    with zipfile.ZipFile(archive_path, 'r') as original:
        with zipfile.ZipFile(temp_path, 'w') as updated:
            
            # Step 1: Write updated files first (like C# code)
            print("   📝 Writing updated files first...")
            for entry_path, new_content in updates:
                print(f"      ✏️  Writing updated: {entry_path}")
                updated.writestr(entry_path, new_content)
            
            # Step 2: Copy rest of files from original (like C# code)
            print("   📋 Copying remaining files from original...")
            for item in original.infolist():
                entry_key = item.filename
                
                # Check if this file was already updated
                is_updated = any(u[0] == entry_key for u in updates)
                
                if not is_updated and entry_key and not entry_key.endswith('/'):
                    print(f"      📄 Copying existing: {entry_key}")
                    data = original.read(item.filename)
                    updated.writestr(item, data)
                elif is_updated:
                    print(f"      ⏭️  Skipping updated: {entry_key}")
                else:
                    print(f"      ⏭️  Skipping directory/empty: {entry_key}")
    
    # Replace original with updated
    os.remove(archive_path)
    os.rename(temp_path, archive_path)
    
    # Analyze AFTER
    detailed_archive_analysis(archive_path, "AFTER ArchiveUpdateService Update")
    
    return archive_path

def simulate_crossplatform_update_logic():
    """Simulate the CrossPlatformArchiveNavigator update logic"""
    print("\n🔄 Simulating CrossPlatformArchiveNavigator logic...")
    
    archive_path = create_complex_test_archive()
    
    # Analyze BEFORE
    detailed_archive_analysis(archive_path, "BEFORE CrossPlatformArchiveNavigator Update")
    
    # Simulate the extract-modify-repack process
    print("\n🔧 Simulating extract-modify-repack process...")
    
    # Step 1: Extract all entries to temp directory (like CrossPlatformArchiveNavigator does)
    temp_extract_dir = tempfile.mkdtemp(prefix='CrossPlatformUpdate_')
    print(f"📁 Temp extract dir: {temp_extract_dir}")
    
    with zipfile.ZipFile(archive_path, 'r') as archive:
        for entry in archive.infolist():
            if not entry.is_dir():
                entry_key = entry.filename
                print(f"📄 Extracting: {entry_key}")
                
                full_path = os.path.join(temp_extract_dir, entry_key)
                dir_path = os.path.dirname(full_path)
                
                if dir_path and not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                
                with archive.open(entry.filename) as source:
                    with open(full_path, 'wb') as target:
                        target.write(source.read())
    
    # Step 2: Modify one file
    file_to_modify = os.path.join(temp_extract_dir, 'file1.txt')
    with open(file_to_modify, 'w') as f:
        f.write('MODIFIED CONTENT via CrossPlatformArchiveNavigator')
    
    # Step 3: Recreate archive (where duplicates might occur)
    print("\n🔄 Recreating archive...")
    temp_archive = archive_path + '.new'
    
    with zipfile.ZipFile(temp_archive, 'w') as new_archive:
        for root, dirs, files in os.walk(temp_extract_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, temp_extract_dir)
                
                # Normalize path separators (potential issue source)
                arc_name = arc_name.replace('\\', '/')
                
                print(f"📄 Adding to archive: {arc_name}")
                new_archive.write(file_path, arc_name)
    
    # Replace original
    os.remove(archive_path)
    os.rename(temp_archive, archive_path)
    
    # Clean up
    shutil.rmtree(temp_extract_dir)
    
    # Analyze AFTER
    detailed_archive_analysis(archive_path, "AFTER CrossPlatformArchiveNavigator Update")
    
    return archive_path

def test_edge_cases():
    """Test specific edge cases that might cause duplicates"""
    print("\n🔍 Testing Edge Cases...")
    
    # Test case 1: Files with similar paths
    print("\n📝 Test Case 1: Similar paths")
    with zipfile.ZipFile('test_similar_paths.zip', 'w') as zf:
        zf.writestr('file.txt', 'Content 1')
        zf.writestr('File.txt', 'Content 2')  # Case difference
        zf.writestr('file.TXT', 'Content 3')  # Extension case difference
    
    detailed_archive_analysis('test_similar_paths.zip', "Similar Paths Test")
    os.remove('test_similar_paths.zip')
    
    # Test case 2: Empty/null keys (simulated)
    print("\n📝 Test Case 2: Path normalization issues")
    with zipfile.ZipFile('test_paths.zip', 'w') as zf:
        zf.writestr('folder/file.txt', 'Content 1')
        zf.writestr('folder\\file2.txt', 'Content 2')  # Backslash path
    
    detailed_archive_analysis('test_paths.zip', "Path Normalization Test")
    os.remove('test_paths.zip')

def main():
    """Main test function"""
    print("🚀 Testing Archive Navigator Pro Duplicate Entry Issues")
    print("=" * 70)
    
    try:
        # Test 1: ArchiveUpdateService logic
        archive1 = simulate_archiveupdateservice_logic()
        
        # Test 2: CrossPlatformArchiveNavigator logic
        archive2 = simulate_crossplatform_update_logic()
        
        # Test 3: Edge cases
        test_edge_cases()
        
        print("\n🔍 SUMMARY:")
        print("=" * 50)
        print("Check the analysis above for any duplicate entries.")
        print("If duplicates are found, they indicate bugs in the save-back logic.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        for file in ['test_complex.zip']:
            if os.path.exists(file):
                os.remove(file)
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 