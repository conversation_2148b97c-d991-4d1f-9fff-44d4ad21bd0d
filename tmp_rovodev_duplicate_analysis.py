#!/usr/bin/env python3
"""
Advanced analysis to identify duplicate archive creation issues
Focus on the actual Archive Navigator Pro save-back process
"""

import os
import zipfile
import tempfile
import shutil
from pathlib import Path

def detailed_archive_analysis(archive_path, name="Archive"):
    """Detailed analysis of archive contents with duplicate detection"""
    print(f"\n🔍 DETAILED ANALYSIS: {name}")
    print("-" * 50)
    
    try:
        with zipfile.ZipFile(archive_path, 'r') as zf:
            entries = zf.namelist()
            print(f"📊 Total entries: {len(entries)}")
            
            # Check for duplicates
            entry_names = [entry.rstrip('/') for entry in entries]
            duplicates = []
            seen = set()
            
            for entry in entry_names:
                if entry in seen and entry not in duplicates:
                    duplicates.append(entry)
                seen.add(entry)
            
            if duplicates:
                print(f"⚠️  DUPLICATES FOUND: {duplicates}")
            else:
                print("✅ No duplicates detected")
            
            # List all entries with details
            for i, entry in enumerate(entries):
                info = zf.getinfo(entry)
                entry_type = "📁 DIR" if entry.endswith('/') else "📄 FILE"
                print(f"   {i+1:2d}. {entry_type} {entry}")
                print(f"       Size: {info.file_size} bytes, Compressed: {info.compress_size} bytes")
                print(f"       Modified: {info.date_time}")
                
    except Exception as ex:
        print(f"❌ Error analyzing {name}: {ex}")

def simulate_archive_navigator_update():
    """Simulate the exact process Archive Navigator Pro uses"""
    print("🔧 Simulating Archive Navigator Pro update process...")
    
    # Create test structure similar to what Archive Navigator Pro handles
    # Create inner content
    inner_content = "Original content\nLine 2"
    
    # Create inner archive
    with zipfile.ZipFile('test_inner.zip', 'w') as inner_zip:
        inner_zip.writestr('data.txt', inner_content)
        inner_zip.writestr('config.ini', '[Settings]\ntest=true')
    
    # Create outer archive
    with zipfile.ZipFile('test_outer.zip', 'w') as outer_zip:
        outer_zip.write('test_inner.zip', 'test_inner.zip')
        outer_zip.writestr('readme.txt', 'Outer readme')
    
    print("✅ Created test archives")
    
    # Analyze BEFORE
    detailed_archive_analysis('test_outer.zip', "BEFORE UPDATE")
    
    # Simulate Archive Navigator Pro's UpdateSingleArchive process
    print("\n🔄 Simulating UpdateSingleArchive process...")
    
    # Step 1: Extract all entries to temp directory (like Archive Navigator Pro does)
    temp_extract_dir = tempfile.mkdtemp(prefix='ArchNavUpdate_')
    print(f"📁 Temp extract dir: {temp_extract_dir}")
    
    with zipfile.ZipFile('test_outer.zip', 'r') as archive:
        for entry in archive.infolist():
            if not entry.is_dir():
                entry_key = entry.filename
                print(f"📄 Processing entry: {entry_key}")
                
                full_path = os.path.join(temp_extract_dir, entry_key)
                dir_path = os.path.dirname(full_path)
                
                if dir_path and not os.path.exists(dir_path):
                    os.makedirs(dir_path)
                    print(f"📁 Created directory: {dir_path}")
                
                # Extract file
                with archive.open(entry.filename) as source:
                    with open(full_path, 'wb') as target:
                        target.write(source.read())
                print(f"✅ Extracted: {entry_key} -> {full_path}")
    
    # Step 2: Modify one of the files (simulate user edit)
    inner_zip_path = os.path.join(temp_extract_dir, 'test_inner.zip')
    
    # Extract inner archive
    inner_temp_dir = tempfile.mkdtemp(prefix='InnerUpdate_')
    with zipfile.ZipFile(inner_zip_path, 'r') as inner_zip:
        inner_zip.extractall(inner_temp_dir)
    
    # Modify data.txt
    data_file = os.path.join(inner_temp_dir, 'data.txt')
    with open(data_file, 'a') as f:
        f.write("\n--- MODIFIED ---")
    print("✏️  Modified data.txt")
    
    # Recreate inner archive
    new_inner_path = inner_zip_path + '.new'
    with zipfile.ZipFile(new_inner_path, 'w') as new_inner:
        for root, dirs, files in os.walk(inner_temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, inner_temp_dir)
                new_inner.write(file_path, arc_name)
                print(f"📄 Added to new inner: {arc_name}")
    
    os.remove(inner_zip_path)
    os.rename(new_inner_path, inner_zip_path)
    
    # Step 3: Recreate outer archive (THIS IS WHERE DUPLICATES MIGHT OCCUR)
    print("\n🔄 Recreating outer archive...")
    
    # List what's in temp directory before recreation
    print("📋 Contents of temp directory before recreation:")
    for root, dirs, files in os.walk(temp_extract_dir):
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, temp_extract_dir)
            print(f"   📄 {rel_path}")
    
    # Recreate archive using the same logic as Archive Navigator Pro
    new_outer_path = 'test_outer.zip.new'
    with zipfile.ZipFile(new_outer_path, 'w') as new_outer:
        for root, dirs, files in os.walk(temp_extract_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, temp_extract_dir)
                
                # POTENTIAL ISSUE: Check if we're adding the same file multiple times
                print(f"📄 Adding to outer archive: {arc_name}")
                new_outer.write(file_path, arc_name)
    
    # Replace original
    os.remove('test_outer.zip')
    os.rename(new_outer_path, 'test_outer.zip')
    
    # Clean up
    shutil.rmtree(temp_extract_dir)
    shutil.rmtree(inner_temp_dir)
    
    # Analyze AFTER
    detailed_archive_analysis('test_outer.zip', "AFTER UPDATE")
    
    # Clean up test files
    os.remove('test_outer.zip')
    os.remove('test_inner.zip')
    
    print("\n🔍 ANALYSIS COMPLETE")
    print("Check the before/after analysis for any duplicate entries")

if __name__ == "__main__":
    print("🚀 Archive Navigator Pro Duplicate Analysis")
    print("=" * 60)
    
    simulate_archive_navigator_update()