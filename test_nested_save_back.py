#!/usr/bin/env python3
"""
Test script to verify if nested archive save-back is actually working
This creates a nested archive and tests the save-back functionality
"""

import os
import time
import zipfile
import tempfile
import shutil
from pathlib import Path

def create_nested_test_archive():
    """Create a nested test archive to verify save-back functionality"""
    print("🔧 Creating nested test archive...")
    
    # Create inner content
    inner_content = "Original inner content for nested save-back testing\nLine 2\nLine 3"
    
    # Step 1: Create inner archive
    with zipfile.ZipFile('inner.zip', 'w') as inner_zip:
        inner_zip.writestr('inner_file.txt', inner_content)
        inner_zip.writestr('config.ini', '[Settings]\nversion=1.0\nmode=test')
    
    print("✅ Created inner.zip")
    
    # Step 2: Create outer archive containing inner archive
    with zipfile.ZipFile('nested_test.zip', 'w') as outer_zip:
        outer_zip.write('inner.zip', 'inner.zip')
        outer_zip.writestr('readme.txt', 'This is a nested archive test')
    
    print("✅ Created nested_test.zip containing inner.zip")
    
    # Clean up temp files
    os.remove('inner.zip')
    
    return 'nested_test.zip'

def verify_nested_archive_content(archive_path):
    """Verify the content of a nested archive"""
    print(f"🔍 Verifying nested archive: {archive_path}")
    
    with zipfile.ZipFile(archive_path, 'r') as outer_zip:
        print(f"   Outer archive files: {outer_zip.namelist()}")
        
        # Extract inner archive to temp
        with tempfile.TemporaryDirectory() as temp_dir:
            inner_zip_path = os.path.join(temp_dir, 'inner.zip')
            
            with outer_zip.open('inner.zip') as inner_zip_data:
                with open(inner_zip_path, 'wb') as f:
                    f.write(inner_zip_data.read())
            
            # Check inner archive content
            with zipfile.ZipFile(inner_zip_path, 'r') as inner_zip:
                print(f"   Inner archive files: {inner_zip.namelist()}")
                
                if 'inner_file.txt' in inner_zip.namelist():
                    content = inner_zip.read('inner_file.txt').decode('utf-8')
                    print(f"   Inner file content: {content[:50]}...")
                    return content
    
    return None

def test_manual_nested_update():
    """Test manual nested archive update to see if our logic works"""
    print("🔄 Testing manual nested archive update...")
    
    archive_path = create_nested_test_archive()
    
    # Verify initial content
    print("\n📋 Initial content verification:")
    original_content = verify_nested_archive_content(archive_path)
    
    if not original_content:
        print("❌ Failed to read initial content")
        return False
    
    # Simulate the update process that Archive Navigator Pro should do
    print("\n🔄 Simulating Archive Navigator Pro update process...")
    
    try:
        # Step 1: Extract outer archive
        temp_dir = tempfile.mkdtemp(prefix='NestedTest_')
        print(f"   📁 Temp directory: {temp_dir}")
        
        with zipfile.ZipFile(archive_path, 'r') as outer_zip:
            outer_zip.extractall(temp_dir)
        
        inner_zip_path = os.path.join(temp_dir, 'inner.zip')
        print(f"   📄 Extracted inner.zip to: {inner_zip_path}")
        
        # Step 2: Extract inner archive
        inner_temp_dir = tempfile.mkdtemp(prefix='InnerTest_')
        
        with zipfile.ZipFile(inner_zip_path, 'r') as inner_zip:
            inner_zip.extractall(inner_temp_dir)
        
        inner_file_path = os.path.join(inner_temp_dir, 'inner_file.txt')
        print(f"   📄 Extracted inner_file.txt to: {inner_file_path}")
        
        # Step 3: Modify the inner file
        modified_content = original_content + "\n\n--- MODIFIED BY NESTED TEST ---\nThis proves nested save-back works!"
        
        with open(inner_file_path, 'w') as f:
            f.write(modified_content)
        
        print(f"   ✏️  Modified inner file content")
        
        # Step 4: Recreate inner archive
        new_inner_zip_path = inner_zip_path + '.new'
        
        with zipfile.ZipFile(new_inner_zip_path, 'w') as new_inner_zip:
            # Copy all files from inner temp dir
            for root, dirs, files in os.walk(inner_temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, inner_temp_dir)
                    new_inner_zip.write(file_path, arc_name)
        
        # Replace old inner archive
        os.remove(inner_zip_path)
        os.rename(new_inner_zip_path, inner_zip_path)
        print(f"   🔄 Recreated inner.zip")
        
        # Step 5: Recreate outer archive
        new_outer_zip_path = archive_path + '.new'
        
        with zipfile.ZipFile(new_outer_zip_path, 'w') as new_outer_zip:
            # Copy all files from temp dir
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, temp_dir)
                    new_outer_zip.write(file_path, arc_name)
        
        # Replace old outer archive
        os.remove(archive_path)
        os.rename(new_outer_zip_path, archive_path)
        print(f"   🔄 Recreated nested_test.zip")
        
        # Clean up
        shutil.rmtree(temp_dir)
        shutil.rmtree(inner_temp_dir)
        
        # Step 6: Verify the changes were saved
        print("\n✅ Final verification:")
        final_content = verify_nested_archive_content(archive_path)
        
        if final_content and "--- MODIFIED BY NESTED TEST ---" in final_content:
            print("   ✅ Nested save-back successful! Modified content found.")
            print(f"   📊 Content length: {len(final_content)} characters")
            return True
        else:
            print("   ❌ Nested save-back failed! Modified content not found.")
            return False
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Clean up
        if os.path.exists(archive_path):
            os.remove(archive_path)

if __name__ == "__main__":
    print("🚀 Testing Nested Archive Save-Back Logic")
    print("=" * 60)
    
    success = test_manual_nested_update()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 NESTED SAVE-BACK LOGIC TEST: PASSED")
        print("✅ The nested archive update logic works correctly!")
        print("📋 Archive Navigator Pro should be able to handle nested archives")
    else:
        print("💥 NESTED SAVE-BACK LOGIC TEST: FAILED")
        print("❌ There's an issue with the nested archive update logic")
    
    exit(0 if success else 1)
