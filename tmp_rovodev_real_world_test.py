#!/usr/bin/env python3
"""
Test with a real nested archive to see if we can reproduce the duplicate issue
that occurs in Archive Navigator Pro
"""

import os
import zipfile
import tarfile
import tempfile
import shutil
from pathlib import Path

def create_complex_nested_archive():
    """Create a more complex nested archive similar to real-world usage"""
    print("🔧 Creating complex nested archive...")
    
    # Create multiple files in inner archive
    inner_files = {
        'config.ini': '[Database]\nhost=localhost\nport=5432',
        'data.txt': 'Important data\nLine 2\nLine 3',
        'script.sh': '#!/bin/bash\necho "Hello World"',
        'readme.md': '# Project\nThis is a test project'
    }
    
    # Create inner archive with multiple files
    with zipfile.ZipFile('inner_complex.zip', 'w') as inner_zip:
        for filename, content in inner_files.items():
            inner_zip.writestr(filename, content)
    
    # Create middle archive with inner archive and additional files
    with zipfile.ZipFile('middle_complex.zip', 'w') as middle_zip:
        middle_zip.write('inner_complex.zip', 'inner_complex.zip')
        middle_zip.writestr('middle_readme.txt', 'Middle level documentation')
        middle_zip.writestr('version.txt', 'v1.0.0')
    
    # Create outer archive
    with zipfile.ZipFile('outer_complex.zip', 'w') as outer_zip:
        outer_zip.write('middle_complex.zip', 'middle_complex.zip')
        outer_zip.writestr('license.txt', 'MIT License')
        outer_zip.writestr('changelog.txt', 'v1.0.0 - Initial release')
    
    # Clean up temp files
    os.remove('inner_complex.zip')
    os.remove('middle_complex.zip')
    
    print("✅ Created outer_complex.zip with nested structure")
    return 'outer_complex.zip'

def analyze_with_file_walking(archive_path, name):
    """Analyze using file walking approach like AddDirectoryToWriter"""
    print(f"\n🔍 FILE WALKING ANALYSIS: {name}")
    print("-" * 50)
    
    # Extract to temp directory
    temp_dir = tempfile.mkdtemp(prefix='FileWalkTest_')
    
    try:
        with zipfile.ZipFile(archive_path, 'r') as zf:
            zf.extractall(temp_dir)
        
        print(f"📁 Extracted to: {temp_dir}")
        
        # Simulate AddDirectoryToWriter logic
        files_found = []
        print("📋 Files found by os.walk (like AddDirectoryToWriter):")
        
        for root, dirs, files in os.walk(temp_dir):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, temp_dir)
                relative_path = relative_path.replace('\\', '/')
                files_found.append(relative_path)
                print(f"   📄 {relative_path}")
        
        # Check for potential duplicates in the walking process
        print(f"\n📊 Total files found: {len(files_found)}")
        
        # Check if any files appear multiple times
        from collections import Counter
        file_counts = Counter(files_found)
        duplicates = {file: count for file, count in file_counts.items() if count > 1}
        
        if duplicates:
            print(f"⚠️  DUPLICATES in file walking: {duplicates}")
        else:
            print("✅ No duplicates in file walking")
        
        return files_found
        
    finally:
        shutil.rmtree(temp_dir)

def simulate_archive_navigator_bug():
    """Try to reproduce the specific bug in Archive Navigator Pro"""
    print("🐛 Attempting to reproduce Archive Navigator Pro duplicate bug...")
    
    archive_path = create_complex_nested_archive()
    
    # Analyze original
    with zipfile.ZipFile(archive_path, 'r') as zf:
        original_entries = zf.namelist()
        print(f"\n📋 ORIGINAL ARCHIVE ENTRIES: {len(original_entries)}")
        for i, entry in enumerate(original_entries):
            print(f"   {i+1}. {entry}")
    
    # Simulate the save-back process with potential for duplicates
    temp_dir = tempfile.mkdtemp(prefix='SaveBackBug_')
    
    try:
        # Extract archive
        with zipfile.ZipFile(archive_path, 'r') as zf:
            zf.extractall(temp_dir)
        
        # Navigate into middle archive
        middle_path = os.path.join(temp_dir, 'middle_complex.zip')
        middle_temp = tempfile.mkdtemp(prefix='MiddleBug_')
        
        with zipfile.ZipFile(middle_path, 'r') as middle_zip:
            middle_zip.extractall(middle_temp)
        
        # Navigate into inner archive and modify
        inner_path = os.path.join(middle_temp, 'inner_complex.zip')
        inner_temp = tempfile.mkdtemp(prefix='InnerBug_')
        
        with zipfile.ZipFile(inner_path, 'r') as inner_zip:
            inner_zip.extractall(inner_temp)
        
        # Modify a file
        config_path = os.path.join(inner_temp, 'config.ini')
        with open(config_path, 'a') as f:
            f.write('\n# Modified by test')
        
        print("✏️  Modified config.ini in inner archive")
        
        # POTENTIAL BUG: Recreate archives with file walking
        # This might be where duplicates are introduced
        
        # Recreate inner archive
        new_inner_path = inner_path + '.new'
        with zipfile.ZipFile(new_inner_path, 'w') as new_inner:
            # Use os.walk like AddDirectoryToWriter
            for root, dirs, files in os.walk(inner_temp):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, inner_temp)
                    arc_name = arc_name.replace('\\', '/')
                    
                    # POTENTIAL ISSUE: Check for _extracted directories
                    if '_extracted' in file_path:
                        print(f"⚠️  SKIPPING _extracted file: {arc_name}")
                        continue
                    
                    new_inner.write(file_path, arc_name)
                    print(f"   📄 Added to inner: {arc_name}")
        
        os.remove(inner_path)
        os.rename(new_inner_path, inner_path)
        
        # Recreate middle archive
        new_middle_path = middle_path + '.new'
        with zipfile.ZipFile(new_middle_path, 'w') as new_middle:
            for root, dirs, files in os.walk(middle_temp):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, middle_temp)
                    arc_name = arc_name.replace('\\', '/')
                    
                    if '_extracted' in file_path:
                        print(f"⚠️  SKIPPING _extracted file: {arc_name}")
                        continue
                    
                    new_middle.write(file_path, arc_name)
                    print(f"   📄 Added to middle: {arc_name}")
        
        os.remove(middle_path)
        os.rename(new_middle_path, middle_path)
        
        # Recreate outer archive
        new_outer_path = archive_path + '.new'
        with zipfile.ZipFile(new_outer_path, 'w') as new_outer:
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, temp_dir)
                    arc_name = arc_name.replace('\\', '/')
                    
                    if '_extracted' in file_path:
                        print(f"⚠️  SKIPPING _extracted file: {arc_name}")
                        continue
                    
                    new_outer.write(file_path, arc_name)
                    print(f"   📄 Added to outer: {arc_name}")
        
        os.remove(archive_path)
        os.rename(new_outer_path, archive_path)
        
        # Analyze result
        with zipfile.ZipFile(archive_path, 'r') as zf:
            final_entries = zf.namelist()
            print(f"\n📋 FINAL ARCHIVE ENTRIES: {len(final_entries)}")
            for i, entry in enumerate(final_entries):
                print(f"   {i+1}. {entry}")
        
        # Compare
        print(f"\n🔍 COMPARISON:")
        print(f"   Original entries: {len(original_entries)}")
        print(f"   Final entries: {len(final_entries)}")
        
        if len(final_entries) > len(original_entries):
            print(f"⚠️  DUPLICATES DETECTED: {len(final_entries) - len(original_entries)} extra entries")
            
            # Find the extras
            original_set = set(original_entries)
            final_set = set(final_entries)
            extras = final_set - original_set
            if extras:
                print(f"   Extra entries: {extras}")
        else:
            print("✅ No duplicates detected")
        
    finally:
        shutil.rmtree(temp_dir)
        shutil.rmtree(middle_temp)
        shutil.rmtree(inner_temp)
        os.remove(archive_path)

if __name__ == "__main__":
    print("🚀 Real-World Archive Navigator Pro Duplicate Bug Test")
    print("=" * 60)
    
    simulate_archive_navigator_bug()